import { GridEventListener } from '@mui/x-data-grid';
import { useWatch } from 'react-hook-form';
import { ChiTietHoaDonBanHangDichVu } from '@/types/schemas';
import InputTableActionBar from './InputTableActionBar';
import { InputTable } from '@/components/custom/arito';
import { getDetailTableColumns } from './columns';
import { FormMode } from '@/types/form';

interface DetailTabProps {
  rows: ChiTietHoaDonBanHangDichVu[];
  selectedRowUuid: string | null;
  handleRowClick: GridEventListener<'rowClick'>;
  handleAddRow: () => void;
  handleDeleteRow: () => void;
  handleCopyRow: () => void;
  handlePasteRow: () => void;
  handleMoveRow: (direction: 'up' | 'down') => void;
  handleCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
  formMode: FormMode;
}

export default function DetailTab({
  rows,
  selectedRowUuid,
  handleRowClick,
  handleAddRow,
  handleDeleteRow,
  handleCopyRow,
  handlePasteRow,
  handleMoveRow,
  handleCellValueChange,
  formMode
}: DetailTabProps) {
  // Sử dụng useWatch để lấy giá trị real-time từ form
  const [ma_ngv] = useWatch({ name: ['ma_ngv'] });
  const [dien_giai] = useWatch({ name: ['dien_giai'] });

  return (
    <div className='h-screen overflow-hidden'>
      <InputTable<ChiTietHoaDonBanHangDichVu>
        rows={rows}
        onRowClick={handleRowClick}
        selectedRowId={selectedRowUuid || undefined}
        columns={getDetailTableColumns(ma_ngv || '1', dien_giai, handleCellValueChange)}
        getRowId={row => row?.uuid || ''}
        mode={formMode}
        actionButtons={
          <InputTableActionBar
            mode={formMode}
            handleAddRow={handleAddRow}
            handleDeleteRow={handleDeleteRow}
            handleCopyRow={handleCopyRow}
            handlePasteRow={handlePasteRow}
            handleMoveRow={handleMoveRow}
            onExport={() => console.log('Export clicked')}
            onPin={() => console.log('Pin clicked')}
          />
        }
        className='h-full'
      />
    </div>
  );
}
