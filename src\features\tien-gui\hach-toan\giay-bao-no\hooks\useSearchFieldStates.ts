import { useState } from 'react';
import type { Account<PERSON>odel, HanThan<PERSON><PERSON><PERSON>, Q<PERSON><PERSON><PERSON>hungTu, TaiKhoan<PERSON>gan<PERSON>ang } from '@/types/schemas';

export const useSearchFieldStates = (initialData?: any) => {
  // STATE
  const [states, setState] = useState({
    // Basic Info
    loaiChungTu: initialData?.ma_ngv || '1',
    bank: initialData?.tknh_data || (null as any | null),
    account: initialData?.tk_data || (null as AccountModel | null),
    quyenChungTu: initialData?.ma_nk_data || (null as QuyenChungTu | null),
    soChungTu: initialData?.so_ct || '',
    loai_hd: initialData?.loai_hd || '1',

    // Thanh toan tab
    hanTT: initialData?.ma_tt_data || (null as HanThanhToan | null),

    // Uy nhiem chi
    donViNhanTien: initialData?.ma_kh0_data || (null as any | null),
    nganHangNhanTien: initialData?.ma_ngan_hang_data || (null as any | null)
  });

  // ACTIONS
  const actions = {
    // Basic Info
    setLoaiChungTu: (value: string) => setState(prev => ({ ...prev, loaiChungTu: value })),
    setBank: (value: TaiKhoanNganHang | null) => setState(prev => ({ ...prev, bank: value })),
    setAccount: (value: AccountModel | null) => setState(prev => ({ ...prev, account: value })),
    setQuyenChungTu: (value: QuyenChungTu | null) => setState(prev => ({ ...prev, quyenChungTu: value })),
    setSoChungTu: (value: string) => setState(prev => ({ ...prev, soChungTu: value })),
    setNhomLoaiHD: (value: string) => setState(prev => ({ ...prev, loai_hd: value })),

    // Thanh toan tab
    setHanTT: (value: HanThanhToan | null) => setState(prev => ({ ...prev, hanTT: value })),

    // Uy nhiem chi
    setDonViNhanTien: (value: any | null) => setState(prev => ({ ...prev, donViNhanTien: value })),
    setNganHangNhanTien: (value: any | null) => setState(prev => ({ ...prev, nganHangNhanTien: value }))
  };

  return {
    state: states,
    actions
  };
};
