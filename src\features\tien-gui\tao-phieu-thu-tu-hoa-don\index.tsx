'use client';

import React, { useEffect, useState } from 'react';

import { useDialogState, useTaoPhieuThuTuHoaDon, useCreateReceipt, useDeleteReceipt } from './hooks';
import { FormDialog, ActionBar, CreateReceiptDialog, DeleteReceiptDialog } from './components';
import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { CreateReceiptFormState } from './hooks/useCreateReceiptFormState';
import { getDataTableColumnsCreate } from './cols-definition';
import { useCRUD, useFormState, useRows } from '@/hooks';
import { CreateReceiptFormValues } from './schema';
import { QUERY_KEYS } from '@/constants';

export default function TaoPhieuThuTuHoaDonPage() {
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);
  const { addItem, updateItem, deleteItem, refreshData, isLoading, data, totalItems, currentPage, handlePageChange } =
    useCRUD<any, any>({
      endpoint: QUERY_KEYS.HOA_DON_BAN_DICH_VU
    });
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();
  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchClose = () => {
    setShowSearchDialog(false);
  };

  const handleSearchSubmit = (filters: any) => {};

  // Hàm xử lý khi checkbox được tick/untick
  const handleCheckboxChange = (invoiceUuid: string, field: string, newValue: boolean) => {
    console.log('Invoice UUID:', invoiceUuid);

    if (newValue) {
      // Thêm UUID vào danh sách đã chọn
      setSelectedInvoices(prev => [...prev, invoiceUuid]);
    } else {
      // Xóa UUID khỏi danh sách đã chọn
      setSelectedInvoices(prev => prev.filter(id => id !== invoiceUuid));
    }
  };

  const handleSubmit = async (data: any) => {
    try {
      if (formMode === 'add') {
        await addItem(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updateItem(selectedObj.uuid, data);
      }
      handleCloseForm();
      clearSelection();
      refreshData();
    } catch (error) {
      return;
    }
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: data,
      columns: getDataTableColumnsCreate(handleCheckboxChange, selectedInvoices)
    },
    {
      name: 'Lập chứng từ',
      rows: data.filter(row => row.status === '0'),
      columns: getDataTableColumnsCreate(handleCheckboxChange, selectedInvoices),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#EAD1DC]' />
    },
    {
      name: 'Chờ duyệt',
      rows: data.filter(row => row.status === '3'),
      columns: getDataTableColumnsCreate(handleCheckboxChange, selectedInvoices),
      icon: <div className='mr-2 size-[7px] rounded-full bg-red-500' />
    },
    {
      name: 'Xóa hóa đơn',
      rows: data.filter(row => row.status === '5'),
      columns: getDataTableColumnsCreate(handleCheckboxChange, selectedInvoices),
      icon: <div className='mr-2 size-[7px] rounded-full bg-[#3D85C6]' />
    },
    {
      name: 'Khác',
      rows: data.filter(row => row.status === '99'),
      columns: getDataTableColumnsCreate(handleCheckboxChange, selectedInvoices),
      icon: <div className='mr-2 size-[7px] rounded-full bg-black' />
    }
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <FormDialog open={showSearchDialog} onClose={handleSearchClose} onSubmit={handleSearchSubmit} />
      )}

      {!showForm && (
        <>
          <ActionBar
            onSearchClick={handleSearch}
            onRefreshClick={handleSearch}
            onCreateReceiptClick={handleSearch}
            onDeleteReceiptClick={handleSearch}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                totalItems={totalItems}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                serverSidePagination={true}
                selectedRowId={selectedRowIndex || undefined}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
