import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  khachHangSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  hoaDonSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  KhachHang,
  DotThanhToan,
  HopDong,
  KheUoc,
  Phi,
  VatTu,
  VuViec
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { calculateTienVnd } from '../../../../utils/calculations';
import { SearchField } from '@/components/custom/arito';

export const getDetailTableColumns = (
  ma_ngv: string,
  dien_giai: string | null | undefined,
  onCellValueChange?: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 120,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục khách hàng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => {
          onCellValueChange?.(params.row.uuid, 'ma_kh_data', row);
        }}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} />
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 150,
    renderCell: params => (
      <CellField
        name='du_cn'
        type='number'
        value={params.row.du_cn || 0}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'du_cn', newValue)}
      />
    )
  },
  // Conditional fields for ma_ngv === '1' (Theo hóa đơn)
  ...(ma_ngv === '1'
    ? [
        {
          field: 'id_hd',
          headerName: 'Hóa đơn',
          width: 180,
          renderCell: (params: any) => (
            <SearchField<any>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOA_DON_THEO_KHACH_HANG}/?customer=${params.row.ma_kh_data?.uuid}&context=BTDCGCN-BH`}
              searchColumns={hoaDonSearchColumns}
              columnDisplay='so_ct'
              dialogTitle='Hóa đơn'
              disabled={!params.row.ma_kh_data?.uuid}
              value={params.row.id_hd_data?.so_ct || ''}
              onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'id_hd_data', row)}
            />
          )
        },
        {
          field: 'ngay_ct_hd',
          headerName: 'Ngày hóa đơn',
          width: 160,
          renderCell: (params: any) => (
            <CellField name='ngay_ct_hd' type='date' value={params.row.id_hd_data?.ngay_hd || params.row.ngay_ct_hd} />
          )
        },
        {
          field: 'tk_co',
          headerName: 'Tài khoản có',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='tk_co' type='text' value={params.row.id_hd_data?.tk_data?.tk || params.row.tk_co} />
          )
        },
        {
          field: 'ma_nt_hd',
          headerName: 'Ngoại tệ',
          width: 100,
          renderCell: (params: any) => (
            <CellField name='ma_nt_hd' type='text' value={params.row.id_hd_data?.ngoai_te || params.row.ma_nt_hd} />
          )
        },
        {
          field: 'ty_gia_hd',
          headerName: 'Tỷ giá hđ',
          width: 100,
          renderCell: (params: any) => (
            <CellField
              name='ty_gia_hd'
              type='number'
              value={params.row.ty_gia_hd || 1}
              onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'ty_gia_hd', newValue)}
            />
          )
        },
        {
          field: 'tien_hd_nt',
          headerName: 'Tiền trên hóa đơn',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='tien_hd_nt'
              type='number'
              value={params.row.id_hd_data?.tien_tren_hd || params.row.tien_hd_nt || 0}
            />
          )
        },
        {
          field: 'da_pb_nt',
          headerName: 'Đã phân bổ',
          width: 120,
          renderCell: (params: any) => <CellField name='da_pb_nt' type='number' value={params.row.da_pb_nt || 0} />
        },
        {
          field: 'cl_nt',
          headerName: 'Còn lại',
          width: 120,
          renderCell: (params: any) => (
            <CellField
              name='cl_nt'
              type='number'
              value={params.row.id_hd_data?.tien_con_phai_tt || params.row.cl_nt || 0}
              onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'cl_nt', newValue)}
            />
          )
        }
      ]
    : []),
  // Conditional fields for ma_ngv === '2' (Theo khách hàng)
  ...(ma_ngv === '2'
    ? [
        {
          field: 'tk_co',
          headerName: 'Tài khoản có',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<AccountModel>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              columnDisplay='code'
              dialogTitle='Danh mục tài khoản'
              value={params.row.tk_co_data?.code || params.row.tk_co}
              onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'tk_co_data', row)}
            />
          )
        },
        {
          field: 'ten_tk_co',
          headerName: 'Tên tài khoản',
          width: 200,
          renderCell: (params: any) => (
            <CellField name='ten_tk_co' type='text' value={params.row.tk_co_data?.name || ''} />
          )
        }
      ]
    : []),
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150,
    renderCell: params => {
      // Calculate the default value using the existing logic
      const calculatedValue = calculateTienVnd(params.row, ma_ngv);
      // Use manual input if available, otherwise use calculated value
      const displayValue =
        params.row.tien_nt !== undefined && params.row.tien_nt !== null ? params.row.tien_nt : calculatedValue;

      return (
        <CellField
          name='tien_nt'
          type='number'
          value={displayValue}
          onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'tien_nt', newValue)}
        />
      );
    }
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250,
    renderCell: params => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || dien_giai || ''}
        onValueChange={newValue => onCellValueChange?.(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || params.row.ma_bp_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || params.row.ma_vv_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || params.row.ma_hd_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || params.row.ma_dtt_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || params.row.ma_ku_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 80,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || params.row.ma_phi_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 150,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || params.row.ma_sp_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 150,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || params.row.ma_lsx_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => (
      <SearchField<ChiPhi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp_data?.ma_cp || params.row.ma_cp_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_cp_data', row)}
      />
    )
  }
];
