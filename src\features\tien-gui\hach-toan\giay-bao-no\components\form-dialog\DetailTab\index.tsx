import { GridCellParams } from '@mui/x-data-grid';
import React, { useEffect, useRef } from 'react';
import { useWatch } from 'react-hook-form';
import { SelectedCellInfo } from '../../../hooks/useInputTableRows';
import { InputTable } from '@/components/custom/arito';
import { getDetailTableColumns } from './columns';
import { FormMode } from '@/types/form';
import { useTaxRate } from '@/hooks';
import ActionBar from './ActionBar';

interface DetailTabProps {
  formMode: FormMode;
  rows: { uuid?: string | null }[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
  status?: string;
  loaiHD?: string;
  setNhomLoaiHD?: (value: string) => void;
}

export const DetailTab: React.FC<DetailTabProps> = ({
  formMode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onCellValueChange,
  status,
  loaiHD,
  setNhomLoaiHD
}) => {
  const watchedDienGiai = useWatch({ name: 'dien_giai' });
  const previousRowsLength = useRef(0);
  const previousDienGiai = useRef(watchedDienGiai);

  useEffect(() => {
    if (watchedDienGiai) {
      if (rows.length > previousRowsLength.current) {
        const newRows = rows.slice(previousRowsLength.current);
        newRows.forEach((row: any) => {
          if (!row.dien_giai && row.uuid) {
            onCellValueChange(row.uuid, 'dien_giai', watchedDienGiai);
          }
        });
      }

      if (previousDienGiai.current !== watchedDienGiai) {
        rows.forEach((row: any) => {
          if (row.dien_giai === previousDienGiai.current && row.uuid) {
            onCellValueChange(row.uuid, 'dien_giai', watchedDienGiai);
          }
        });
      }
    }

    previousRowsLength.current = rows.length;
    previousDienGiai.current = watchedDienGiai;
  }, [watchedDienGiai, rows, onCellValueChange]);

  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDetailTableColumns(onCellValueChange, status, loaiHD, setNhomLoaiHD)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <ActionBar
          formMode={formMode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
          handleExport={() => console.log('Export clicked')}
          handlePin={() => console.log('Pin clicked')}
          handleViewInventory={() => console.log('View inventory clicked')}
          handleViewReceipt={() => console.log('View receipt clicked')}
        />
      }
    />
  );
};
