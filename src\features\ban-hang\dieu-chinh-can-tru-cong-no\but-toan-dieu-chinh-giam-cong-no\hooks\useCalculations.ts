import { useMemo } from 'react';
import { calculateTotalAmount } from '../utils/calculations';

export const useCalculations = (detailRows: any[], ma_ngv?: string) => {
  // Calculate total amount by summing up "Tiền VND" from all detail rows
  // For ma_ngv = 1: Tiền VND = Còn lại (cl_nt) × Tỷ giá hóa đơn (ty_gia_hd)
  // For ma_ngv = 2: Tiền VND = manually entered value
  const totalAmount = useMemo(() => {
    return calculateTotalAmount(detailRows, ma_ngv);
  }, [detailRows, ma_ngv]);

  return {
    totalAmount
  };
};
