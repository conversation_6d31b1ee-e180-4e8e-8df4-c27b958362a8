import { useState, useEffect } from 'react';
import {
  ButToanKetChuyen,
  ButToanKetChuyenInput,
  ButToanKetChuyenResponse,
  ButToanKetChuyenFilterParams
} from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import { GeneralJournalSchema } from '../schemas';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface UseButToanKetChuyenReturn {
  butToanKetChuyens: ButToanKetChuyen[];
  isLoading: boolean;
  isLoadingDetail: boolean;
  isSuccess: boolean;
  error: string | null;
  createdReceiptId: string | null;
  addButToanKetChuyen: (newButToanKetChuyen: ButToanKetChuyenInput) => Promise<ButToanKetChuyen>;
  deleteButToanKetChuyen: (uuid: string) => Promise<void>;
  refreshButToanKetChuyens: () => Promise<void>;
  getButToanKetChuyensByFilter: (filters: ButToanKetChuyenFilterParams) => Promise<ButToanKetChuyen[]>;
  getButToanKetChuyenDetail: (uuid: string) => Promise<ButToanKetChuyen>;
  createGeneralJournal: (formValues: GeneralJournalSchema) => Promise<any>;
  ketChuyenButToan: (selectedItems: ButToanKetChuyen[]) => Promise<any>;
}

// Test with a simpler endpoint first
// const TEST_ENDPOINT = 'accounts'; // Simple endpoint to test API connectivity

/**
 * Hook for managing ButToanKetChuyen (General Journal Transfer) data
 *
 * This hook provides functions to fetch, create, update, and delete general journal transfers.
 */
export const useButToanKetChuyen = (initialButToanKetChuyens: ButToanKetChuyen[] = []): UseButToanKetChuyenReturn => {
  const [butToanKetChuyens, setButToanKetChuyens] = useState<ButToanKetChuyen[]>(initialButToanKetChuyens);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingDetail, setIsLoadingDetail] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [createdReceiptId, setCreatedReceiptId] = useState<string | null>(null);

  const { entity, loading: authLoading } = useAuth();

  const fetchButToanKetChuyens = async () => {
    if (!entity?.slug) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const url = `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_KET_CHUYEN}/`;
      const response = await api.get(url);

      // Handle different response structures
      let results = [];
      if (response.data?.results) {
        results = response.data.results;
      } else if (Array.isArray(response.data)) {
        results = response.data;
      } else if (response.data?.data) {
        results = Array.isArray(response.data.data) ? response.data.data : [response.data.data];
      }

      setButToanKetChuyens(results);
    } catch (error: any) {
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const getButToanKetChuyensByFilter = async (filters: ButToanKetChuyenFilterParams): Promise<ButToanKetChuyen[]> => {
    if (!entity?.slug) {
      return [];
    }

    setIsLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();

      if (filters.ky1) {
        queryParams.append('ky1', filters.ky1.toString());
      }

      if (filters.ky2) {
        queryParams.append('ky2', filters.ky2.toString());
      }

      if (filters.nam) {
        queryParams.append('nam', filters.nam.toString());
      }

      if (filters.ma_unit !== undefined && filters.ma_unit !== null) {
        const trimmedUnit = filters.ma_unit.toString().trim();

        if (trimmedUnit !== '') {
          queryParams.append('ma_unit', trimmedUnit);
        }
      }

      const url = `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_KET_CHUYEN}/?${queryParams.toString()}`;
      const response = await api.get(url);

      // Handle different response structures
      let results = [];
      if (response.data?.results) {
        results = response.data.results;
      } else if (Array.isArray(response.data)) {
        results = response.data;
      } else if (response.data?.data) {
        results = Array.isArray(response.data.data) ? response.data.data : [response.data.data];
      }

      return results;
    } catch (error: any) {
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const addButToanKetChuyen = async (newButToanKetChuyen: ButToanKetChuyenInput): Promise<ButToanKetChuyen> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoading(true);
    try {
      const response = await api.post<ButToanKetChuyen>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_KET_CHUYEN}/`,
        newButToanKetChuyen
      );

      const addedButToanKetChuyen = response.data;
      setButToanKetChuyens(prev => [...prev, addedButToanKetChuyen]);
      setIsSuccess(true);
      return addedButToanKetChuyen;
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteButToanKetChuyen = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_KET_CHUYEN}/${uuid}/`);
      setButToanKetChuyens(prev => prev.filter(item => item.uuid !== uuid));
      setIsSuccess(true);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const getButToanKetChuyenDetail = async (uuid: string): Promise<ButToanKetChuyen> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoadingDetail(true);
    try {
      const response = await api.get<ButToanKetChuyen>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_KET_CHUYEN}/${uuid}/`
      );
      return response.data;
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
      throw error;
    } finally {
      setIsLoadingDetail(false);
    }
  };

  /**
   * Create general journal from search parameters
   * @param formValues Form values containing search parameters
   * @returns Promise that resolves when the journal is created
   */
  const createGeneralJournal = async (formValues: GeneralJournalSchema) => {
    // Reset states
    setIsLoading(true);
    setIsSuccess(false);
    setError(null);
    setCreatedReceiptId(null);

    try {
      // For now, just simulate the API call
      // In the future, this should call the actual API endpoint for creating journal entries
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simulate successful response
      const data = {
        id: 'JOURNAL-' + Math.floor(Math.random() * 1000000),
        message: 'General journal created successfully',
        filters: formValues
      };

      // Set success state
      setIsSuccess(true);
      setCreatedReceiptId(data.id);

      return data;
    } catch (err) {
      // Set error state
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      throw err;
    } finally {
      // Reset loading state
      setIsLoading(false);
    }
  };

  /**
   * Kết chuyển bút toán - Send selected items to backend
   * @param selectedItems Array of selected ButToanKetChuyen items
   * @returns Promise that resolves when the transfer is completed
   */
  const ketChuyenButToan = async (selectedItems: ButToanKetChuyen[]) => {
    if (!entity?.slug) throw new Error('Entity not found');
    if (selectedItems.length === 0) throw new Error('No items selected');

    setIsLoading(true);
    setError(null);

    try {
      // Prepare data for backend - only required fields
      const transferData = selectedItems.map(item => ({
        id: item.id,
        stt: item.stt,
        ten_btkc: item.ten_btkc,
        loai_bt: item.loai_bt,
        nhom_bt: item.nhom_bt,
        tk_no: item.tk_no,
        tk_co: item.tk_co
      }));

      const response = await api.post(`/entities/${entity.slug}/erp/${QUERY_KEYS.BUT_TOAN_KET_CHUYEN}/ket-chuyen/`, {
        items: transferData
      });

      setIsSuccess(true);
      return response.data;
    } catch (error: any) {
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Don't make API calls while auth is still loading
    if (authLoading) {
      return;
    }

    if (entity?.slug) {
      // Test API connectivity first
      //   testApiConnectivity();
      // Then fetch actual data
      fetchButToanKetChuyens();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug, authLoading]);

  return {
    butToanKetChuyens,
    isLoading,
    isLoadingDetail,
    isSuccess,
    error,
    createdReceiptId,
    addButToanKetChuyen,
    deleteButToanKetChuyen,
    refreshButToanKetChuyens: fetchButToanKetChuyens,
    getButToanKetChuyensByFilter,
    getButToanKetChuyenDetail,
    createGeneralJournal,
    ketChuyenButToan
  };
};
