import { useState, useEffect } from 'react';
import { GiayBaoNo, GiayBaoNoInput, GiayBaoNoResponse } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseGiayBaoNoReturn {
  giayBaoNos: GiayBaoNo[];
  isLoading: boolean;
  isLoadingDetail: boolean;
  addGiayBaoNo: (newGiayBaoNo: GiayBaoNoInput) => Promise<GiayBaoNo>;
  updateGiayBaoNo: (uuid: string, updatedGiayBaoNo: GiayBaoNoInput) => Promise<GiayBaoNo>;
  deleteGiayBaoNo: (uuid: string) => Promise<void>;
  refreshGiayBaoNos: () => Promise<void>;
  getGiayBaoNoByCustomer: (customerId: string) => Promise<GiayBaoNo[]>;
  getGiayBaoNoDetail: (uuid: string) => Promise<[]>;
}

/**
 * Hook for managing GiayBaoNo (Sales Invoice) data
 *
 * This hook provides functions to fetch, create, update, and delete sales invoices and their details.
 */
export const useGiayBaoNo = (initialGiayBaoNos: GiayBaoNo[] = []): UseGiayBaoNoReturn => {
  const [giayBaoNos, setGiayBaoNos] = useState<GiayBaoNo[]>(initialGiayBaoNos);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingDetail, setIsLoadingDetail] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchGiayBaoNos = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<GiayBaoNoResponse>(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_NO}/`);
      setGiayBaoNos(response.data.results);
    } catch (error) {
      console.error('Error fetching sales invoices:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getGiayBaoNoByCustomer = async (customerId: string): Promise<GiayBaoNo[]> => {
    if (!entity?.slug) return [];

    setIsLoading(true);
    try {
      const response = await api.get<GiayBaoNoResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_NO}/?ma_kh=${customerId}`
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching sales invoices by customer:', error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const addGiayBaoNo = async (newGiayBaoNo: GiayBaoNoInput): Promise<GiayBaoNo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoading(true);
    try {
      const response = await api.post<GiayBaoNo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_NO}/`,
        newGiayBaoNo
      );

      const addedGiayBaoNo = response.data;
      setGiayBaoNos(prev => [...prev, addedGiayBaoNo]);
      return addedGiayBaoNo;
    } catch (error) {
      console.error('Error adding sales invoice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateGiayBaoNo = async (uuid: string, updatedGiayBaoNo: GiayBaoNoInput): Promise<GiayBaoNo> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoading(true);
    try {
      const response = await api.put<GiayBaoNo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_NO}/${uuid}/`,
        updatedGiayBaoNo
      );

      const updatedData = response.data;
      setGiayBaoNos(prev => prev.map(item => (item.uuid === uuid ? updatedData : item)));
      return updatedData;
    } catch (error) {
      console.error('Error updating sales invoice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteGiayBaoNo = async (uuid: string) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_NO}/${uuid}/`);
      setGiayBaoNos(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting sales invoice:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const getGiayBaoNoDetail = async (uuid: string): Promise<[]> => {
    if (!entity?.slug) throw new Error('Entity not found');

    setIsLoadingDetail(true);
    try {
      const response = await api.get<[]>(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIAY_BAO_NO}/${uuid}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching sales invoice detail:', error);
      throw error;
    } finally {
      setIsLoadingDetail(false);
    }
  };

  useEffect(() => {
    fetchGiayBaoNos();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug]);

  return {
    giayBaoNos,
    isLoading,
    isLoadingDetail,
    addGiayBaoNo,
    updateGiayBaoNo,
    deleteGiayBaoNo,
    refreshGiayBaoNos: fetchGiayBaoNos,
    getGiayBaoNoByCustomer,
    getGiayBaoNoDetail
  };
};
