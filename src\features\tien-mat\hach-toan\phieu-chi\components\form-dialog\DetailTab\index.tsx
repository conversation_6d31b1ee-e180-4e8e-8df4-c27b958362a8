import { GridCellParams, type GridEventListener } from '@mui/x-data-grid';
import React, { useEffect, useState } from 'react';
import { useWatch } from 'react-hook-form';
import { SelectedCellInfo } from '../hooks/useDetailRows';
import { InputTable } from '@/components/custom/arito';
import type { FormFieldState } from '../../../hooks';
import { getDetailTableColumns } from './columns';
import { FormMode } from '@/types/form';
import ActionBar from './ActionBar';

interface DetailTabProps {
  formMode: FormMode;
  rows: any[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: GridEventListener<'rowClick'>;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const DetailTab: React.FC<DetailTabProps> = ({
  formMode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onCellValueChange
}) => {
  const [ma_ngv, dien_giai] = useWatch({ name: ['ma_ngv', 'dien_giai'] });
  const [displayTax, setDisplayTax] = useState(false);
  useEffect(() => {
    setDisplayTax(rows.some(row => Number(row.ma_loai_hd) > 0));
  }, [rows]);
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDetailTableColumns(ma_ngv, dien_giai, displayTax, onCellValueChange)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <ActionBar
          formMode={formMode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
          handleExport={() => console.log('Export clicked')}
          handlePin={() => console.log('Pin clicked')}
        />
      }
    />
  );
};
