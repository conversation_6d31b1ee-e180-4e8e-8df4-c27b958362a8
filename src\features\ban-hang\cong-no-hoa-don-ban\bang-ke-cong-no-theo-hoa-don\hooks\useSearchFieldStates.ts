import { useState } from 'react';

/**
 * Hook for managing search field states in the bang-ke-cong-no-theo-hoa-don feature
 *
 * This hook manages all SearchField states that are not handled by AritoForm directly.
 * SearchField components use onRowSelection to update these states.
 */
export function useSearchFieldStates() {
  const [customer, setCustomer] = useState<any>(null);

  const [customerGroup1, setCustomerGroup1] = useState<any>(null);
  const [customerGroup2, setCustomerGroup2] = useState<any>(null);
  const [customerGroup3, setCustomerGroup3] = useState<any>(null);

  const [region, setRegion] = useState<any>(null);

  /**
   * Get search field data for API requests
   * Returns the UUIDs or codes of selected items
   */
  const getSearchFieldData = () => {
    return {
      ma_kh: customer?.uuid || null,
      nh_kh1: customerGroup1?.uuid || null,
      nh_kh2: customerGroup2?.uuid || null,
      nh_kh3: customerGroup3?.uuid || null,
      rg_code: region?.uuid || null
    };
  };

  /**
   * Reset all search field states
   */
  const resetSearchFields = () => {
    setCustomer(null);
    setCustomerGroup1(null);
    setCustomerGroup2(null);
    setCustomerGroup3(null);
    setRegion(null);
  };

  return {
    customer,
    setCustomer,

    customerGroup1,
    setCustomerGroup1,
    customerGroup2,
    setCustomerGroup2,
    customerGroup3,
    setCustomerGroup3,

    region,
    setRegion,

    getSearchFieldData,
    resetSearchFields
  };
}
