import { useState } from 'react';
import { SearchFormValues } from '../schemas';

/**
 * Hook for managing search dialog state in the giay-bao-co feature
 * @returns Search state and handlers
 */
export const useSearchState = () => {
  const [openSearchDialog, setOpenSearchDialog] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues | null>(null);

  const handleOpenSearchDialog = () => {
    setOpenSearchDialog(true);
  };

  const handleCloseSearchDialog = () => {
    setOpenSearchDialog(false);
  };

  const handleSearch = (data: SearchFormValues) => {
    setSearchParams(data);
  };

  return {
    openSearchDialog,
    searchParams,

    setOpenSearchDialog,
    setSearchParams,

    handleOpenSearchDialog,
    handleCloseSearchDialog,
    handleSearch
  };
};
