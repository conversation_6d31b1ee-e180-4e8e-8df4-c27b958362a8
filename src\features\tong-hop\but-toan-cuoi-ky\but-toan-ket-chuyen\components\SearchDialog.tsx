import React, { useState } from 'react';
import { AritoDialog, AritoForm, AritoIcon, BottomBar } from '@/components/custom/arito';
import { generalJournalFilterSchema, GeneralJournalFilterSchema } from '../schemas';
import { MainTab } from './filter-tabs/BasicInfoTab';
import { DonViCoSo } from '@/types/schemas';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (data: GeneralJournalFilterSchema) => void;
}

const initialSearchValues = {
  ky1: new Date().getMonth() + 1,
  ky2: new Date().getMonth() + 1,
  nam: new Date().getFullYear(),
  ma_unit: undefined
};

const SearchDialog: React.FC<SearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [selectedUnit, setSelectedUnit] = useState<DonViCoSo | null>(null);

  const handleSubmit = (data: GeneralJournalFilterSchema) => {
    // Include the selected unit's UUID in the search data
    const searchData = {
      ...data,
      ma_unit: selectedUnit?.uuid || data.ma_unit || undefined
    };
    onSearch(searchData);
  };

  const handleUnitChange = (unit: DonViCoSo | null) => {
    setSelectedUnit(unit);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Bút toán kết chuyển'
      maxWidth='md'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={generalJournalFilterSchema}
        onSubmit={handleSubmit}
        initialData={initialSearchValues}
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <MainTab onUnitChange={handleUnitChange} />
          </div>
        }
        bottomBar={<BottomBar mode='add' onSubmit={() => {}} onClose={onClose} />}
        classNameBottomBar='relative w-full flex justify-end gap-2'
      />
    </AritoDialog>
  );
};

export default SearchDialog;
