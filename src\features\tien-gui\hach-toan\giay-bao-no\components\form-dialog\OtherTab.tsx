import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface OtherTabProps {
  formMode: FormMode;
  initialData: any;
}

export function OtherTab({ formMode, initialData }: OtherTabProps) {
  return (
    <div className='p-4'>
      <div className='flex items-center'>
        <Label className='w-40'>Mã đối tượng</Label>
        <FormField name='ma_kh' type='text' disabled={true} value={initialData?.ma_kh_data?.customer_code} />
        <span className={'text-sm text-gray-400'}>{initialData?.ma_kh_data?.customer_name}</span>
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Kèm theo</Label>
        <FormField name='so_ct_goc' type='number' disabled={formMode === 'view'} />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Chứng từ gốc</Label>
        <FormField name='dien_giai_ct_goc' type='text' disabled={formMode === 'view'} />
      </div>
    </div>
  );
}
