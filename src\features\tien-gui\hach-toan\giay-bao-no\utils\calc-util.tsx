/**
 * Calculate totals from detail rows
 */
export const calculateTotals = (detailRows: any[] = [], taxRows: any[] = [], feeRows: any[] = []) => {
  const tong_tien = detailRows.reduce((sum: number, row: { tien_nt: number }) => sum + (Number(row.tien_nt) || 0), 0);
  const tong_thue = taxRows.reduce((sum: number, row: { t_thue_nt: number }) => sum + (Number(row.t_thue_nt) || 0), 0);
  const tong_phi = feeRows.reduce(
    (sum: number, row: { tien_cp_nt: number; t_thue_nt: number }) =>
      sum + (Number(row.tien_cp_nt) + Number(row.t_thue_nt) || 0),
    0
  );
  const tong_thanh_toan = tong_tien + tong_thue + tong_phi;

  return {
    tong_tien,
    tong_thue,
    tong_phi,
    tong_thanh_toan
  };
};
