interface BottomBarProps {
  totalAmount: number;
}

export default function BottomBar({ totalAmount = 0 }: BottomBarProps) {
  // Format number with commas and 2 decimal places to match CellField formatting
  const formatNumber = (num: number): string => {
    if (num === null || num === undefined || isNaN(num)) return '0.00';
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  return (
    <div className='w-full border-t bg-white px-4 py-2'>
      <div className='flex'>
        <div className='flex flex-col'>
          <div className='flex items-center pr-6'>
            <span className='mr-2 font-medium'>Tổng tiền VND:</span>
            <span className='font-semibold text-blue-600'>{formatNumber(totalAmount)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
