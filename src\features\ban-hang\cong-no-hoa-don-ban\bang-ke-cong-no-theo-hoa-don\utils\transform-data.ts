import { SearchFormValues } from '../schema';

/**
 * Transform search form data for API submission
 * Combines form data with search field states data
 * @param formData - Form data from the search form
 * @param searchFieldData - Data from search field states
 * @returns Transformed data ready for API request
 */
export const transformSearchData = (formData: SearchFormValues, searchFieldData: any = {}) => {
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  return {
    ngay_bc: formatDate(formData.ngay_bc),
    ngay_tt: formatDate(formData.ngay_tt),
    ngay_ct1: formData.ngay_ct1 ? formatDate(formData.ngay_ct1) : '',
    ngay_ct2: formData.ngay_ct2 ? formatDate(formData.ngay_ct2) : '',

    ct_theo: formData.ct_theo,
    tt_yn: formData.tt_yn,
    so_ngay_tt: formData.so_ngay_tt,
    so_ngay_cb: formData.so_ngay_cb,

    so_ct1: formData.so_ct1 || '',
    so_ct2: formData.so_ct2 || '',

    ma_unit: formData.ma_unit,
    mau_bc: formData.mau_bc,

    data_analysis_struct: '',

    ma_kh: searchFieldData.ma_kh || null,
    nh_kh1: searchFieldData.nh_kh1 || null,
    nh_kh2: searchFieldData.nh_kh2 || null,
    nh_kh3: searchFieldData.nh_kh3 || null,
    rg_code: searchFieldData.rg_code || null
  };
};

/**
 * Transform API response data if needed
 * @param apiResponse - Raw API response
 * @returns Transformed response data
 */
export const transformApiResponse = (apiResponse: any) => {
  return {
    results: apiResponse.results || [],
    count: apiResponse.count || 0,
    next: apiResponse.next,
    previous: apiResponse.previous,
    summary: apiResponse.summary || {}
  };
};
