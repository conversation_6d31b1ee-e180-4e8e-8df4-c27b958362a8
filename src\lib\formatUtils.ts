/**
 * Utility functions for formatting numbers, currency values, and dates
 */

import { format } from 'date-fns';

/**
 * Format a number as currency in Vietnamese format
 * @param value - The number to format
 * @returns Formatted currency string
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(value);
}

/**
 * Format a number in Vietnamese format
 * @param value - The number to format
 * @returns Formatted number string
 */
export const formatNumber = (num: number | string): string => {
  // Convert to number, preserving decimal points
  const parsed = typeof num === 'string' ? parseFloat(num) : Number(num);

  if (isNaN(parsed) || parsed === 0) return '0';

  return parsed.toLocaleString('en-US');
};

export function formatMoney(value: number | string): string {
  if (value == null) return '';

  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numValue)) return '';

  return numValue.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

/**
 * Format a number as currency in Vietnamese format and replace the currency symbol with 'VND'
 * @param value - The number to format
 * @returns Formatted currency string with 'VND' instead of '₫'
 */
export function formatCurrencyWithVND(value: number): string {
  return formatCurrency(value).replace('₫', 'VND');
}

/**
 * Format a date to dd/MM/yyyy format
 * @param date - The date to format (can be Date object, string, or null/undefined)
 * @returns Formatted date string in dd/MM/yyyy format, or empty string if invalid
 */
export function formatDate(date: Date | string | null | undefined): string {
  if (!date) return '';

  try {
    let dateObj: Date;

    if (typeof date === 'string') {
      // Handle yyyymmdd format (e.g., "20240315")
      if (date.length === 8 && /^\d{8}$/.test(date)) {
        const year = parseInt(date.substring(0, 4));
        const month = parseInt(date.substring(4, 6)) - 1; // Month is 0-indexed in Date
        const day = parseInt(date.substring(6, 8));
        dateObj = new Date(year, month, day);
      }
      // Handle various other string formats
      else if (date.includes('-')) {
        // Handle ISO format (yyyy-mm-dd) or other dash-separated formats
        dateObj = new Date(date);
      } else if (date.includes('/')) {
        // Handle slash-separated formats
        dateObj = new Date(date);
      } else {
        // Try to parse as-is
        dateObj = new Date(date);
      }
    } else {
      dateObj = date;
    }

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    // Use date-fns for consistent formatting
    return format(dateObj, 'dd/MM/yyyy');
  } catch (error) {
    return '';
  }
}
