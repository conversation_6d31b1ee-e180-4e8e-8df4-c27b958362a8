import { z } from 'zod';

export const generalJournalSchema = z.object({
  ky1: z.coerce.number().min(1).max(12),
  ky2: z.coerce.number().min(1).max(12),
  nam: z.coerce.number().min(2000).max(2100),
  ma_unit: z
    .string()
    .transform(val => (val === '' ? undefined : val))
    .optional()
});

export const generalJournalFilterSchema = z.object({
  ky1: z.coerce.number().min(1).max(12),
  ky2: z.coerce.number().min(1).max(12),
  nam: z.coerce.number().min(2000).max(2100),
  ma_unit: z
    .string()
    .transform(val => (val === '' ? undefined : val))
    .optional()
});

export type GeneralJournalSchema = z.infer<typeof generalJournalSchema>;
export type GeneralJournalFilterSchema = z.infer<typeof generalJournalFilterSchema>;

export const initialGeneralJournalValues: GeneralJournalSchema = {
  ky1: new Date().getMonth() + 1,
  ky2: new Date().getMonth() + 1,
  nam: new Date().getFullYear(),
  ma_unit: undefined
};
