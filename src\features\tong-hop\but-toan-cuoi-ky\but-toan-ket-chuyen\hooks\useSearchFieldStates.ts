import { useState } from 'react';
import { DonVi } from '@/types/schemas';

export interface FormFieldState {
  // Unit information
  donVi: DonVi | null;
}

export interface FormFieldActions {
  // Search field setters
  setDonVi: (donVi: DonVi) => void;

  // Utility functions
  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  // Unit information
  donVi: null
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    // Unit information
    donVi: initialData.ma_unit_data || null
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    // Search field setters
    setDonVi: (donVi: DonVi) => {
      setState(prev => ({
        ...prev,
        donVi
      }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}

/**
 * @deprecated Use useFormFieldState instead
 * Hook for managing search field states in the but-toan-ket-chuyen feature
 *
 * @returns Object with states and setters for search fields
 */
export const useSearchFieldStates = () => {
  // Document type state
  const [donVi, setDonVi] = useState<DonVi | null>(null);

  return {
    donVi,
    setDonVi
  };
};
