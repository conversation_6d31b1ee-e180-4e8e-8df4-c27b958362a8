import { customerSearchColumns, nganHangSearchColumns, QUERY_KEYS } from '@/constants';
import { FormField, SearchField } from '@/components/custom/arito';
import type { <PERSON><PERSON>ch<PERSON><PERSON>, NganHang } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import type { FormMode } from '@/types/form';

interface UyNhiemChiTabProps {
  formMode: FormMode;
  state: any;
  actions: any;
}

export const UyNhiemChiTab: React.FC<UyNhiemChiTabProps> = ({ formMode = 'add', state, actions }) => {
  return (
    <div className='space-y-3 p-4'>
      <div className='flex items-center gap-x-1'>
        <Label className='mt-3 min-w-[160px]'>Mã đ/v nhận tiền</Label>
        <SearchField<KhachHang>
          searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
          searchColumns={customerSearchColumns}
          columnDisplay='customer_code'
          displayRelatedField='customer_name'
          dialogTitle='Danh mục đối tượng'
          value={state.donViNhanTien?.customer_code || ''}
          relatedFieldValue={state.donViNhanTien?.customer_name || ''}
          disabled={formMode === 'view'}
          onRowSelection={actions.setDonViNhanTien}
        />
      </div>

      <div className='flex items-center gap-x-1'>
        <Label className='mt-3 min-w-[160px]'>Loại chuyển tiền</Label>
        <FormField
          name='loai_lenh'
          type='select'
          options={[
            {
              value: '1',
              label: 'Trong ngân hàng'
            },
            {
              value: '2',
              label: 'Chuyển thường ngoài ngân hàng mã Citad'
            },
            {
              value: '3',
              label: 'Chuyển nhanh ngoài ngân hàng mã Napas'
            }
          ]}
          disabled={formMode === 'view'}
          className='min-w-48'
        />
      </div>
      <div className='flex items-center gap-x-1'>
        <Label className='mt-3 min-w-[160px]'>Ngân hàng nhận tiền</Label>
        <SearchField<NganHang>
          value={state.nganHangNhanTien?.ma_ngan_hang}
          displayRelatedField='ten_ngan_hang'
          searchEndpoint={`/${QUERY_KEYS.NGAN_HANG}`}
          searchColumns={nganHangSearchColumns}
          columnDisplay='ma_ngan_hang'
          relatedFieldValue={state.nganHangNhanTien?.ten_ngan_hang}
          dialogTitle='Danh sách ngân hàng'
          disabled={formMode === 'view'}
          onRowSelection={actions.setNganHangNhanTien}
        />
      </div>

      <div className='flex items-center gap-x-1'>
        <Label className='mt-3 min-w-[160px]'>Số tài khoản khách</Label>
        <FormField name='stk_kh' type='text' disabled={formMode === 'view'} className='w-64' />
      </div>

      <div className='flex items-center gap-x-1'>
        <Label className='mt-3 min-w-[160px]'>Tên đơn vị nhận tiền</Label>
        <FormField name='ten_kh' type='text' disabled={formMode === 'view'} className='w-96' />
      </div>

      <div className='flex items-center gap-x-8'>
        <div className='flex items-center gap-x-1'>
          <Label className='mt-3 min-w-[160px]'>Chi nhánh</Label>
          <FormField name='chi_nhanh_nh' type='text' disabled={formMode === 'view'} className='w-80' />
        </div>

        <div className='flex items-center gap-x-1'>
          <Label className='mt-3 min-w-24'>Tỉnh thành</Label>
          <FormField name='tinh_thanh_nh' type='text' disabled={formMode === 'view'} className='w-full' />
        </div>
      </div>
      <div className='flex items-center gap-x-1'>
        <Label className='mt-3 min-w-[160px]'>Nội dung</Label>
        <FormField name='dien_giai_nh' type='text' disabled={formMode === 'view'} className='min-w-96' />
      </div>

      <div className='flex items-center gap-x-1'>
        <FormField
          name='phi_nhan_yn'
          type='checkbox'
          label='Người nhận chịu phí ngân hàng'
          disabled={formMode === 'view'}
          className='flex items-center gap-x-2'
        />
      </div>
    </div>
  );
};

export default UyNhiemChiTab;
