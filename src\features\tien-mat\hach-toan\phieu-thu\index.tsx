'use client';

import Split from 'react-split';
import { AritoDataTables, LoadingOverlay, InputTable, DeleteDialog } from '@/components/custom/arito';
import { SearchDialog, ActionBar, PhieuThuForm, InputTableActionBar } from './components';
import { useDataTables, useSearchFieldStates, usePhieuThuDetail } from './hooks';
import { PhieuThuInput, PhieuThu } from '@/types/schemas';
import { getInputTableColumns } from './cols-definition';
import { useRows, useFormState } from '@/hooks';

export default function PhieuThuPage() {
  const { selectedObj, handleRowClick, clearSelection, selectedRowIndex } = useRows<PhieuThu>();
  const { handleViewClick } = useFormState();
  const { addPhieuThu, updatePhieuThu, deletePhieuThu, refreshPhieuThus, tables, isLoading } =
    useDataTables(handleViewClick);
  const {
    showForm,
    showSearch,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleDeleteClick,
    handleCopyClick,

    handleSearch,
    handleCloseSearch
  } = useFormState();
  const searchFieldStates = useSearchFieldStates(selectedObj);
  const { detail, fetchDetail } = usePhieuThuDetail(selectedObj?.uuid);

  const handleFormSubmit = async (data: PhieuThuInput) => {
    try {
      if (formMode === 'add') {
        await addPhieuThu(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updatePhieuThu(selectedObj.uuid, data);
      }

      clearSelection();
      handleCloseForm();
      clearSelection();
      await refreshPhieuThus();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const handleSearchSubmit = (filters: any) => {
    console.log('Search filters:', filters);
    // TODO: Implement search functionality here
  };

  return (
    <>
      <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
        {showForm && (
          <PhieuThuForm
            formMode={formMode}
            initialData={
              selectedObj && (formMode === 'edit' || formMode === 'view' || (formMode === 'add' && isCopyMode))
                ? selectedObj
                : formMode === 'add' && !isCopyMode
                  ? undefined
                  : undefined
            }
            searchFieldStates={searchFieldStates}
            onSubmit={handleFormSubmit}
            onClose={handleCloseForm}
          />
        )}

        {showDelete && (
          <DeleteDialog
            open={showDelete}
            onClose={handleCloseDelete}
            selectedObj={selectedObj}
            deleteObj={deletePhieuThu}
            clearSelection={clearSelection}
          />
        )}

        {!showForm && (
          <>
            <SearchDialog open={showSearch} onClose={handleCloseSearch} onSearch={handleSearchSubmit} />

            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onSearchClick={handleSearch}
              onRefreshClick={async () => {
                await refreshPhieuThus();
                await fetchDetail();
              }}
              isEditDisabled={!selectedObj}
              isDeleteDisabled={!selectedObj}
              isCopyDisabled={!selectedObj}
            />
            <Split
              className='flex flex-1 flex-col'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={8}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                {isLoading && <LoadingOverlay />}
                {!isLoading && (
                  <AritoDataTables
                    tables={tables}
                    onRowClick={handleRowClick}
                    selectedRowId={selectedRowIndex || undefined}
                  />
                )}
              </div>

              <div className='w-full overflow-hidden'>
                <InputTable
                  rows={detail || []}
                  columns={getInputTableColumns()}
                  mode={formMode}
                  getRowId={row => row?.uuid || ''}
                  className='w-full'
                  actionButtons={<InputTableActionBar mode={formMode} />}
                />
              </div>
            </Split>
          </>
        )}
      </div>
    </>
  );
}
