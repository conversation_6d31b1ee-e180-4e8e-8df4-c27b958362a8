import { GridColDef, GridCellParams } from '@mui/x-data-grid';
import { format } from 'date-fns';

export const getDataTableColumns = (handleViewClick: () => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.value;
      switch (status) {
        case '0':
          return 'Chưa ghi sổ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi sổ';
        case '6':
          return 'Khác';
        default:
          return '';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 150,
    renderCell: params => (
      <button onClick={handleViewClick} className='hover:text-blue-500 hover:underline'>
        {params.row.so_ct}
      </button>
    )
  },
  {
    field: 'ngay_ct',
    headerName: '<PERSON>ày c/từ',
    width: 120,
    renderCell: params => format(params.row.ngay_ct, 'dd/MM/yyyy')
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'sten_tknh',
    headerName: 'Ngân hàng',
    width: 150,
    renderCell: params => (
      <span>
        [{params.row.tknh_data?.ma_ngan_hang_data?.ma_ngan_hang}] {params.row.tknh_data?.tknh}
      </span>
    )
  },
  {
    field: 'tk',
    headerName: 'Tk có',
    width: 100,
    renderCell: params => params.row.tk_data?.code
  },
  {
    field: 't_tt_nt',
    headerName: 'Tổng thanh toán',
    width: 150,
    renderCell: params => {
      const t_tien = Number(params.row.t_tien_nt) || 0;
      const t_thue = Number(params.row.t_thue_nt) || 0;
      const t_cp = Number(params.row.t_cp_nt) || 0;
      return t_tien + t_thue + t_cp;
    }
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => params.row.ma_nt_data?.ma_nt
  },
  {
    field: 'ma_ngv',
    headerName: 'Loại chứng từ',
    width: 200,
    renderCell: params => {
      const status = params.row.ma_ngv;
      switch (status) {
        case '1':
          return '1. Chi theo hóa đơn';
        case '2':
          return '2. Chi theo đối tượng';
        case '3':
          return '3. Chi khác';
        case '4':
          return '4. Rút tiền về nhập quỹ';
        case '5':
          return '5. Chuyển giữa các ngân hàng';
        case '6':
          return '6. Mua hàng chi tiền ngay';
        default:
          return '';
      }
    }
  }
];

export const getInputTableColumns = (data?: any[]): GridColDef[] => {
  const hasDataForField = (fieldPath: string): boolean => {
    if (!data || data.length === 0) return false;

    return data.some(row => {
      const fieldParts = fieldPath.split('.');
      let value = row;

      for (const part of fieldParts) {
        value = value?.[part];
        if (value === undefined || value === null) return false;
      }

      return value !== '' && value !== 0;
    });
  };

  const columns = [
    {
      field: 'dien_giai',
      headerName: 'Diễn giải',
      width: 300,
      renderCell: (params: GridCellParams) => params.row.dien_giai
    },
    {
      field: 'ma_kh',
      headerName: 'Mã đối tượng',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.ma_kh_data?.customer_code
    },
    {
      field: 'ten_kh',
      headerName: 'Tên đối tượng',
      width: 200,
      renderCell: (params: GridCellParams) => params.row.ma_kh_data?.customer_name
    },

    hasDataForField('du_cn') && {
      field: 'du_cn',
      headerName: 'Dư công nợ',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.du_cn
    },

    hasDataForField('id_hd_data.so_ct') && {
      field: 'id_hd',
      headerName: 'Hóa đơn',
      width: 100,
      renderCell: (params: GridCellParams) => params.row.id_hd_data?.so_ct
    },

    hasDataForField('id_hd_data.ngay_ct') && {
      field: 'ngay_hd',
      headerName: 'Ngày hóa đơn',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.id_hd_data?.ngay_ct
    },

    hasDataForField('tk_co_data.code') && {
      field: 'tk_co',
      headerName: 'Tài khoản có',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.tk_co_data?.code
    },

    hasDataForField('id_hd_data.ngoai_te') && {
      field: 'ma_nt',
      headerName: 'Ngoại tệ',
      width: 100,
      renderCell: (params: GridCellParams) => params.row.id_hd_data?.ngoai_te
    },

    hasDataForField('id_hd_data.ty_gia_hd') && {
      field: 'ty_gia_hd',
      headerName: 'Tỷ giá hđ',
      width: 100,
      renderCell: (params: GridCellParams) => params.row.id_hd_data?.ty_gia_hd
    },

    hasDataForField('id_hd_data.da_pb_nt') && {
      field: 'da_pb_nt',
      headerName: 'Đã phân bổ',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.id_hd_data?.da_pb_nt
    },

    hasDataForField('id_hd_data.cl_nt') && {
      field: 'cl_nt',
      headerName: 'Còn lại',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.id_hd_data?.cl_nt
    },

    hasDataForField('tien_nt') && {
      field: 'tien_nt',
      headerName: 'Tiền VND',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.tien_nt
    },

    hasDataForField('ma_bp_data.ma_bp') && {
      field: 'ma_bp',
      headerName: 'Bộ phận',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.ma_bp_data?.ma_bp
    },

    hasDataForField('ma_vv_data.ma_vu_viec') && {
      field: 'ma_vv',
      headerName: 'Vụ việc',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.ma_vv_data?.ma_vu_viec
    },

    hasDataForField('ma_hd_data.ma_hd') && {
      field: 'ma_hd',
      headerName: 'Hợp đồng',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.ma_hd_data?.ma_hd
    },

    hasDataForField('ma_dtt_data.ma_dtt') && {
      field: 'ma_dtt',
      headerName: 'Đợt thanh toán',
      width: 150,
      renderCell: (params: GridCellParams) => params.row.ma_dtt_data?.ma_dtt
    },

    hasDataForField('ma_ku_data.ma_ku') && {
      field: 'ma_ku',
      headerName: 'Khế ước',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.ma_ku_data?.ma_ku
    },

    hasDataForField('ma_phi_data.ma_phi') && {
      field: 'ma_phi',
      headerName: 'Phí',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.ma_phi_data?.ma_phi
    },

    hasDataForField('ma_sp_data.ma_vt') && {
      field: 'ma_sp',
      headerName: 'Sản phẩm',
      width: 120,
      renderCell: (params: GridCellParams) => params.row.ma_sp_data?.ma_vt
    },

    hasDataForField('ma_cp0_data.ma_cpkhl') && {
      field: 'ma_cp0',
      headerName: 'C/p không h/lệ',
      width: 180,
      renderCell: (params: GridCellParams) => params.row.ma_cp0_data?.ma_cpkhl
    },

    hasDataForField('ma_loai_hd') && {
      field: 'ma_loai_hd',
      headerName: 'Loại hóa đơn',
      width: 120
    },

    hasDataForField('ma_thue') && {
      field: 'ma_thue',
      headerName: 'Thuế suất',
      width: 100
    },

    hasDataForField('ma_mau_bc') && {
      field: 'ma_mau_bc',
      headerName: 'Mẫu báo cáo',
      width: 120
    },

    hasDataForField('ma_tc_thue') && {
      field: 'ma_tc_thue',
      headerName: 'Mã tính chất',
      width: 120
    }
  ];

  return columns.filter(Boolean) as GridColDef[];
};

export const historyColumns: GridColDef[] = [
  { field: 'username', headerName: 'Tài khoản', width: 100 },
  { field: 'nickname', headerName: 'Tên tài khoản', width: 200 },
  { field: 'action', headerName: 'Hành động', width: 100 },
  { field: 'datetime0', headerName: 'Thời gian', width: 150 }
];
