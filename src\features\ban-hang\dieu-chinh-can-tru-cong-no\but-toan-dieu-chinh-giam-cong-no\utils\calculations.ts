/**
 * Calculate "Tiền VND" for a single row
 * Formula:
 * - Always prioritize manually entered tien_nt value if available
 * - For ma_ngv = 1 (<PERSON>): Tiền VND = Còn lại (cl_nt) × Tỷ giá hóa đơn (ty_gia_hd)
 * - For ma_ngv = 2 (<PERSON>): Use manually entered tien_nt value
 *
 * @param row - Detail row data
 * @param ma_ngv - Type of receipt (1 = by invoice, 2 = by customer)
 * @returns Calculated or manually entered Tiền VND amount
 */
export const calculateTienVnd = (row: any, ma_ngv?: string): number => {
  console.log('calculateTienVnd - row:', row.uuid, 'ma_ngv:', ma_ngv, 'row.tien_nt:', row.tien_nt);

  // Always prioritize manually entered value if available
  if (row.tien_nt !== undefined && row.tien_nt !== null) {
    const result = Number(row.tien_nt);
    console.log('calculateTienVnd - using manual value, result:', result);
    return result;
  }

  // For ma_ngv = 2 (<PERSON>), use manually entered value (fallback to 0)
  if (ma_ngv === '2') {
    const result = 0;
    console.log('calculateTienVnd - ma_ngv=2, no manual value, result:', result);
    return result;
  }

  // For ma_ngv = 1 (Theo hóa đơn), calculate from invoice data
  // Get "Còn lại" amount from invoice data or row data
  const conLai = Number(row.id_hd_data?.tien_con_phai_tt || row.cl_nt || 0);

  // Get exchange rate from row data (default to 1)
  const tyGiaHd = Number(row.ty_gia_hd || 1);

  // Calculate from conLai * tyGiaHd
  // Round to 2 decimal places to avoid floating point precision issues
  const result = Math.round(conLai * tyGiaHd * 100) / 100;
  console.log('calculateTienVnd - ma_ngv=1, conLai:', conLai, 'tyGiaHd:', tyGiaHd, 'result:', result);
  return result;
};

/**
 * Calculate total amount from all detail rows
 *
 * @param detailRows - Array of detail row data
 * @param ma_ngv - Type of receipt (1 = by invoice, 2 = by customer)
 * @returns Total amount (sum of all Tiền VND)
 */
export const calculateTotalAmount = (detailRows: any[], ma_ngv?: string): number => {
  const total = detailRows.reduce((sum, row) => {
    const rowAmount = calculateTienVnd(row, ma_ngv);
    console.log('calculateTotalAmount - row:', row.uuid, 'ma_ngv:', ma_ngv, 'rowAmount:', rowAmount);
    return sum + rowAmount;
  }, 0);

  console.log('calculateTotalAmount - total:', total, 'detailRows.length:', detailRows.length);
  return total;
};
