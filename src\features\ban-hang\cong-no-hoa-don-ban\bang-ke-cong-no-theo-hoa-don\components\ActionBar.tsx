import { <PERSON><PERSON>, <PERSON>er, Refresh<PERSON><PERSON>, Search, Sheet } from 'lucide-react';
import { AritoIcon, AritoActionBar, AritoMenuButton, AritoActionButton } from '@/components/custom/arito';
import { formatDate } from '@/lib/formatUtils';
import { SearchFormValues } from '../schema';

interface ActionBarProps {
  onSearchClick?: () => void;
  onPrintClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  className?: string;
  searchParams?: SearchFormValues;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className,
  searchParams
}) => {
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'><PERSON><PERSON>ng kê công nợ theo hóa đơn</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>
                {searchParams?.ngay_bc ? `Ngày báo cáo: ${formatDate(searchParams.ngay_bc)}` : `Ngày báo cáo: `}
              </span>
            </p>
          </div>
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}
      <AritoMenuButton
        title='In ấn'
        icon={Printer}
        items={[
          {
            title: 'Báo cáo công nợ theo hóa đơn',
            icon: <AritoIcon icon={20} />,
            group: 0
          },
          {
            title: 'Báo cáo hóa đơn sắp hết hạn thanh toán',
            icon: <AritoIcon icon={20} />,
            group: 0
          },
          {
            title: 'Báo cáo hóa đơn đã quá hạn thanh toán - Song ngữ',
            icon: <AritoIcon icon={20} />,
            group: 2
          }
        ]}
      />

      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
