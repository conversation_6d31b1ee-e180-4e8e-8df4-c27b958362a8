import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import { MA_CHUNG_TU } from '@/constants';
import { FormMode } from '@/types/form';

const transformDetailRows = (detailRows: any[], formMode: FormMode) => {
  return detailRows?.map((row: any, index: number) => ({
    line: index + 1,
    ...(isValidUUID(row.uuid) && formMode !== 'add' && { uuid: row.uuid }),
    dien_giai: row.dien_giai || null,
    ma_kh: row.ma_kh_data?.uuid || null,
    id_hd: row.id_hd_data?.ID || row.id_hd || null,
    tk_no: row.id_hd_data?.tk_data?.uuid || null,
    tien_nt: row.tien_nt || row.id_hd_data?.tien_tren_hd || null,
    line_dn: row.line_dn || null,
    ma_bp: row.ma_bp_data?.uuid || null,
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_sp: row.ma_sp_data?.uuid || null,
    ma_lsx: row.ma_lsx || null,
    ma_cp0: row.ma_cp0_data?.uuid || null
  }));
};

const transformThueRows = (thueRows: any[], formMode: FormMode) => {
  return thueRows?.map((row: any, index: number) => ({
    line: index + 1,
    ...(isValidUUID(row.uuid) && formMode !== 'add' && { uuid: row.uuid }),
    so_ct0: row.so_ct0 || '',
    so_ct2: row.so_ct2 || '',
    ngay_ct0: row.ngay_ct0 || '',

    ma_thue: row.ma_thue_data?.uuid || '',
    ma_mau_ct: row.ma_mau_ct_data?.uuid || '',
    ma_mau_bc: row.mau_bao_cao || '1',
    ma_tc_thue: row.ma_tc_thue_data?.uuid || '',

    ma_kh: row.ma_kh_data?.uuid || '',
    ten_kh_thue: row.ma_kh_data?.customer_name || '',
    dia_chi: row.ma_kh_data?.address || '',
    ma_so_thue: row.ma_kh_data?.tax_code || '',

    ten_vt_thue: row.ten_vt_thue || '',

    t_tien_nt: row.t_tien_nt || 0.0,
    t_thue_nt: row.t_thue_nt || 0.0,

    tk_thue_no: row.tk_thue_no_data?.uuid || '',
    tk_du: row.tk_du_data?.uuid || '',

    ma_kh9: row.ma_kh9_data?.uuid || '',

    ma_tt: row.ma_tt_data?.uuid || '',

    ghi_chu: row.ghi_chu || '',

    ma_bp: row.ma_bp_data?.uuid || null,
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_sp: row.ma_sp_data?.uuid || null,
    ma_lsx: row.ma_lsx_data?.uuid || null,
    ma_cp0: row.ma_cp0_data?.uuid || null
  }));
};

const transformPhiNganHangRows = (phiNganHangRows: any[], formMode: FormMode) => {
  return phiNganHangRows?.map((row: any, index: number) => ({
    line: index + 1,
    ...(isValidUUID(row.uuid) && formMode !== 'add' && { uuid: row.uuid }),
    ma_kh: row.ma_kh_data?.uuid || '',

    ma_cpnh: row.ma_cpnh_data?.uuid || '',
    tien_cp_nt: row.tien_cp_nt || 0.0,

    so_ct0: row.so_ct0 || '',
    so_ct2: row.so_ct2 || '',
    ngay_ct0: row.ngay_ct0 || new Date().toISOString().split('T')[0],

    dien_giai: row.dien_giai || '',

    tk_cpnh: row.tk_cpnh_data?.uuid || '',
    tk_du: row.tk_du_data?.uuid || '',
    tk_thue: row.tk_thue_data?.uuid || '',

    ma_thue: row.ma_thue_data?.uuid || '',
    t_thue_nt: row.t_thue_nt || 0.0,

    ma_bp: row.ma_bp_data?.uuid || null,
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_sp: row.ma_sp_data?.uuid || null,
    ma_lsx: row.ma_lsx_data?.uuid || null,
    ma_cp0: row.ma_cp0_data?.uuid || null
  }));
};

export const transformFormData = (
  data: any,
  state: any,
  totals: any,
  entityUnit: any,
  detailsRow: any[],
  taxRows: any[],
  feeRows: any[],
  formMode: FormMode
) => {
  console.log(detailsRow);

  const detail = transformDetailRows(detailsRow, formMode);
  const tax = transformThueRows(taxRows, formMode);
  const fee = transformPhiNganHangRows(feeRows, formMode);

  // Build the final form data object
  return {
    ...data,

    unit_id: entityUnit?.uuid,
    ma_ngv: state.loaiChungTu || '1',
    tknh: state.bank?.uuid || '',
    tk: state.account?.uuid || '',
    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.TIEN_GUI.GIAY_BAO_NO),
    loai_hd: state.loai_hd || '1',
    ma_kh0: state.donViNhanTien?.uuid || '',
    ma_ngan_hang: state.nganHangNhanTien?.uuid || '',
    ma_tt: state.hanTT?.uuid || '',
    so_ct0: data.so_ct0,
    ma_kh: detail[0]?.ma_kh,

    t_tien_nt: totals.tong_tien,
    t_thue_nt: totals.tong_thue,
    t_cp_nt: totals.tong_phi,
    t_tt_nt: totals.tong_thanh_toan,
    chi_tiet: detail,
    thue: tax,
    phi_ngan_hang: fee
  };
};
