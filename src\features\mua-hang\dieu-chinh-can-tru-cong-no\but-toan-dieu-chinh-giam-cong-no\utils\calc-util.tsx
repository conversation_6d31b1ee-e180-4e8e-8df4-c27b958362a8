/**
 * Calculate totals from detail rows
 */
export const calculateTotals = (detailRows: any[] = []) => {
  const tongTien = detailRows.reduce((sum: number, row: { tien_nt: number }) => sum + (row.tien_nt || 0), 0);
  const tongThanhToan = tongTien;
  return {
    tongTien,
    tongThanhToan
  };
};

export const handleUpdateRowFields = (row: any, field: string, newValue: any) => {
  if (field === 'id_hd_data' || field === 'ma_thue_data' || field === 'tien_nt') {
    const tien_nt = field === 'id_hd_data' ? newValue?.tien_tren_hd : field === 'tien_nt' ? newValue : row.tien_nt;
    const thue_suat = field === 'ma_thue_data' ? newValue?.thue_suat : row.thue_suat || 0;
    const thue_nt = (tien_nt * thue_suat) / 100;
    return {
      tien_nt,
      thue_suat,
      thue_nt
    };
  }
  return {};
};
