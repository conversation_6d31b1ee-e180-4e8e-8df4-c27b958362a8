import { <PERSON>Field } from '@/components/custom/arito';
import { formatMoney } from '@/lib/formatUtils';
import { FormFieldState } from '../../../hooks';
import { Label } from '@/components/ui/label';

interface Props {
  tong_tien: number;
  tong_thanh_toan: number;
  tong_thue: number;
  state: FormFieldState;
}

export function BottomBar({ tong_tien, tong_thanh_toan, tong_thue, state }: Props) {
  return (
    <div className='w-full border-t bg-white px-4 py-2'>
      <div className='flex'>
        <div className='ml-4 flex flex-col'>
          <div className='flex items-center'>
            <Label className='w-24 font-medium'>Tổng tiền</Label>
            <FormField
              name='t_tien_nt'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(tong_tien)}
            />
            <FormField
              name='t_tien'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(tong_tien)}
              className='hidden'
            />
          </div>
        </div>

        <div className='ml-auto flex w-1/4 flex-col'>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng thanh toán</Label>
            <FormField
              name='t_tt_nt'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={tong_tien === 0 && tong_thue > 0 ? `-` + formatMoney(tong_thue) : formatMoney(tong_thanh_toan)}
            />
            <FormField
              name='t_tt'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(tong_thanh_toan)}
              className='hidden'
            />
          </div>
        </div>
      </div>
    </div>
  );
}
