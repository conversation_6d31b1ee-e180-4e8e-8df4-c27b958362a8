import { useState, useCallback } from 'react';
import {
  BangKeCongNoTheoHoaDonItem,
  BangKeCongNoTheoHoaDonMuaResponse,
  BangKeCongNoTheoHoaDonSearchFormValues,
  UseBangKeCongNoTheoHoaDonReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BangKeCongNoTheoHoaDonItem[] => {
  const currentDate = new Date();
  const suppliers = [
    { ma_kh: 'NCC001', ten_kh: 'Công ty TNHH ABC Technology' },
    { ma_kh: 'NCC002', ten_kh: 'Công ty Cổ phần XYZ Solutions' },
    { ma_kh: 'NCC003', ten_kh: 'Doanh nghiệp DEF Industries' },
    { ma_kh: 'NCC004', ten_kh: 'Công ty TNHH GHI Manufacturing' },
    { ma_kh: 'NCC005', ten_kh: 'Tập đoàn JKL Group' },
    { ma_kh: 'NCC006', ten_kh: 'Công ty MNO Trading' },
    { ma_kh: 'NCC007', ten_kh: 'Doanh nghiệp PQR Services' },
    { ma_kh: 'NCC008', ten_kh: 'Công ty STU Logistics' },
    { ma_kh: 'NCC009', ten_kh: 'Tổng công ty VWX Corporation' },
    { ma_kh: 'NCC010', ten_kh: 'Công ty YZ Global' }
  ];

  const currencies = ['VND', 'USD', 'EUR'];
  const paymentTypes = ['TM', 'CK', 'TT'];
  const documentTypes = ['HD', 'PN', 'PC'];
  const salesReps = [
    { ma_nvbh: 'NV001', ten_nvbh: 'Nguyễn Văn An' },
    { ma_nvbh: 'NV002', ten_nvbh: 'Trần Thị Bình' },
    { ma_nvbh: 'NV003', ten_nvbh: 'Lê Văn Cường' },
    { ma_nvbh: 'NV004', ten_nvbh: 'Phạm Thị Dung' },
    { ma_nvbh: 'NV005', ten_nvbh: 'Hoàng Văn Em' }
  ];

  return Array.from({ length: 15 }, (_, index) => {
    const supplier = suppliers[index % suppliers.length];
    const currency = currencies[index % currencies.length];
    const paymentType = paymentTypes[index % paymentTypes.length];
    const docType = documentTypes[index % documentTypes.length];
    const salesRep = salesReps[index % salesReps.length];

    const docDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - (index % 6), index + 1);
    const dueDate = new Date(docDate.getTime() + (30 + index * 5) * 24 * 60 * 60 * 1000);
    const paymentDate = index % 3 === 0 ? new Date(docDate.getTime() + (20 + index * 2) * 24 * 60 * 60 * 1000) : '';

    const totalAmount =
      (50000000 + index * 10000000) * (currency === 'USD' ? 0.00004 : currency === 'EUR' ? 0.000037 : 1);
    const paidAmount = index % 4 === 0 ? totalAmount * 0.5 : index % 3 === 0 ? totalAmount * 0.8 : 0;
    const remainingAmount = totalAmount - paidAmount;

    const daysDiff = Math.floor((currentDate.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
    const isOverdue = daysDiff > 0;

    return {
      stt: index + 1,
      id: `${index + 1}`,
      id_tt: `TT${String(index + 1).padStart(3, '0')}`,
      id_ct: `CT${String(index + 1).padStart(3, '0')}`,
      loai_tt: paymentType,
      unit_id: 'CN',
      ma_ct: docType,
      ngay_ct: docDate.toISOString().split('T')[0],
      so_ct: `${docType}${currentDate.getFullYear()}${String(index + 1).padStart(3, '0')}`,
      ma_tt: paymentType,
      han_tt: dueDate.toISOString().split('T')[0],
      ma_nt: currency,
      ty_gia: currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1,
      ma_kh: supplier.ma_kh,
      ma_nvbh: salesRep.ma_nvbh,
      dien_giai: `Mua hàng hóa từ ${supplier.ten_kh} - Tháng ${docDate.getMonth() + 1}/${docDate.getFullYear()}`,
      t_tt_nt0: totalAmount,
      t_tt0: totalAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1),
      t_tt: totalAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1),
      tt: paidAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1),
      cl: remainingAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1),
      tt_yn: paidAmount > 0 ? '1' : '0',
      ngay_tt: paymentDate ? paymentDate.toISOString().split('T')[0] : '',
      trong_han: !isOverdue ? remainingAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1) : 0,
      qua_han01:
        isOverdue && daysDiff <= 30
          ? remainingAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1)
          : 0,
      qua_han02:
        isOverdue && daysDiff > 30 && daysDiff <= 60
          ? remainingAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1)
          : 0,
      qua_han03:
        isOverdue && daysDiff > 60 && daysDiff <= 90
          ? remainingAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1)
          : 0,
      qua_han04:
        isOverdue && daysDiff > 90 && daysDiff <= 180
          ? remainingAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1)
          : 0,
      qua_han05:
        isOverdue && daysDiff > 180
          ? remainingAmount * (currency === 'USD' ? 24500 : currency === 'EUR' ? 27000 : 1)
          : 0,
      so_ct0: `DH${currentDate.getFullYear()}${String(index + 1).padStart(3, '0')}`,
      ngay_ct0: new Date(docDate.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      ngay_dh0: new Date(docDate.getTime() - 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      so_ngay: Math.abs(daysDiff),
      ten_kh: supplier.ten_kh,
      ten_nvbh: salesRep.ten_nvbh,
      ma_unit: 'CN'
    };
  });
};

/**
 * Custom hook for managing BangKeCongNoTheoHoaDon (Purchase Invoice Debt Report) data
 *
 * This hook provides functionality to fetch purchase invoice debt data
 * with mock support for testing and development purposes.
 */
export function useBangKeCongNoTheoHoaDon(
  searchParams: BangKeCongNoTheoHoaDonSearchFormValues
): UseBangKeCongNoTheoHoaDonReturn {
  const [data, setData] = useState<BangKeCongNoTheoHoaDonItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BangKeCongNoTheoHoaDonSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BangKeCongNoTheoHoaDonMuaResponse>(
        '/mua-hang/cong-no-hoa-don-mua/bang-ke-cong-no-theo-hoa-don/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
