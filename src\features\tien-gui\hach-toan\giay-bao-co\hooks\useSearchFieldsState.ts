import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@/types/schemas';

/**
 * Hook for managing search field selections in the search dialog
 * @returns Search field states and setters
 */
export const useSearchFieldsState = () => {
  const [selectedTaiKhoanNo, setSelectedTaiKhoanNo] = useState<TaiKhoan | null>(null);
  const [selectedKhachHang, setSelectedKhachHang] = useState<KhachHang | null>(null);
  const [selectedTaiKhoanCo, setSelectedTaiKhoanCo] = useState<TaiKhoan | null>(null);
  const [selectedDonVi, setSelectedDonVi] = useState<DonVi | null>(null);

  const resetSearchFields = () => {
    setSelectedTaiKhoanNo(null);
    setSelectedKhachHang(null);
    setSelectedTaiKhoanCo(null);
    setSelectedDonVi(null);
  };

  return {
    selectedTai<PERSON><PERSON>an<PERSON><PERSON>,
    selected<PERSON><PERSON><PERSON><PERSON><PERSON>,
    selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    selected<PERSON><PERSON><PERSON><PERSON>,

    setSelected<PERSON>aiKhoanNo,
    setSelectedKhachHang,
    setSelectedTaiKhoanCo,
    setSelectedDonVi,

    resetSearchFields
  };
};
