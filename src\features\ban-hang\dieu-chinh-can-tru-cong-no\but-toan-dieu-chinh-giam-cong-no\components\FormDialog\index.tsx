import { useWatch } from 'react-hook-form';
import { BasicInfo, DetailTab, ExchangeRateTab, OtherTab, BottomBar } from './tabs';
import { useCalculations, useFormFieldStates } from '../../hooks';
import { FormSchema, initialFormValues } from '../../schema';
import { calculateTienVnd } from '../../utils/calculations';
import { AritoForm } from '@/components/custom/arito';
import { useAuth } from '@/contexts/auth-context';
import { transformFormData } from '../../utils';
import { useInputTableRows } from '@/hooks';
import { FormMode } from '@/types/form';

interface FormDialogProps {
  formMode: FormMode;
  initialData: any;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
}

export default function FormDialog({ formMode, initialData, onClose, onSubmit }: FormDialogProps) {
  const { entityUnit } = useAuth();
  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  } = useInputTableRows<any>(initialData?.chi_tiet_data || []);

  const formFieldStates = useFormFieldStates(initialData);

  const handleSubmit = (data: any) => {
    // Calculate total amount using the same logic as the bottom bar
    const ma_ngv = data.ma_ngv || '1';
    const totalAmount = rows.reduce((sum, row) => {
      const rowAmount = calculateTienVnd(row, ma_ngv);
      return sum + rowAmount;
    }, 0);

    const formData = transformFormData(data, formFieldStates, rows, totalAmount, entityUnit, formMode);

    if (formMode === 'edit') {
      // In edit mode, preserve the original i_so_ct to avoid validation errors
      onSubmit({
        ...formData,
        i_so_ct: Math.max(initialData?.i_so_ct || 1, formFieldStates?.quyenChungTu?.kieu_trung_so === '2' ? 3 : 1)
      });
    } else {
      onSubmit(formData);
    }
  };

  return (
    <AritoForm
      schema={FormSchema}
      initialData={initialData || initialFormValues}
      onSubmit={handleSubmit}
      mode={formMode}
      title={formMode === 'add' ? 'Mới' : undefined}
      subTitle='Bút toán điều chỉnh giảm công nợ'
      onClose={onClose}
      headerFields={<BasicInfo formMode={formMode} formFieldStates={formFieldStates} />}
      bottomBar={<DynamicBottomBar rows={rows} />}
      tabs={[
        {
          id: '1',
          label: 'Chi tiết',
          component: (
            <DetailTab
              formMode={formMode}
              rows={rows}
              selectedRowUuid={selectedRowUuid}
              handleRowClick={handleRowClick}
              handleAddRow={handleAddRow}
              handleDeleteRow={handleDeleteRow}
              handleCopyRow={handleCopyRow}
              handlePasteRow={handlePasteRow}
              handleMoveRow={handleMoveRow}
              handleCellValueChange={handleCellValueChange}
            />
          )
        },
        {
          id: '2',
          label: 'Tỷ giá',
          component: <ExchangeRateTab formMode={formMode} />
        },
        {
          id: '3',
          label: 'Khác',
          component: <OtherTab formMode={formMode} />
        }
      ]}
    />
  );
}

// Component that can access form context for dynamic bottom bar
function DynamicBottomBar({ rows }: { rows: any[] }) {
  // Get current ma_ngv value from form context
  const [ma_ngv] = useWatch({ name: ['ma_ngv'] });
  const { totalAmount } = useCalculations(rows, ma_ngv || '1');

  // Debug logging
  console.log('DynamicBottomBar - ma_ngv:', ma_ngv, 'rows:', rows, 'totalAmount:', totalAmount);

  return <BottomBar totalAmount={totalAmount} />;
}
