import {
  SearchField,
  FormField,
  AritoHeaderTabs,
  TabItem,
  DocumentNumberField,
  CurrencyInput
} from '@/components/custom/arito';
import { accountSearchColumns, MA_CHUNG_TU, QUERY_KEYS } from '@/constants';
import { InfoTabFormFieldStates } from '../../../hooks';
import { AccountModel } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoProps {
  formMode: FormMode;
  formFieldStates: InfoTabFormFieldStates;
}

export default function BasicInfo({ formMode, formFieldStates }: BasicInfoProps) {
  const isViewMode = formMode === 'view';
  const { taiKhoan, setTaiKhoan, quyenChungTu, setQuyenChungTu, soChungTu, setSoChungTu } = formFieldStates;

  const InforTab = (
    <div className='grid grid-cols-[3fr_1fr] gap-8 p-4'>
      <div className='space-y-4'>
        <div className='flex items-center space-x-4'>
          <Label className='w-32 shrink-0'>Loại phiếu thu</Label>
          <FormField
            type='select'
            className='w-[200px]'
            name='ma_ngv'
            options={[
              { value: '1', label: '1. Theo hóa đơn' },
              { value: '2', label: '2. Theo khách hàng' }
            ]}
            disabled={isViewMode}
          />
        </div>

        <div className='flex items-center space-x-4'>
          <Label className='w-32 shrink-0'>Diễn giải</Label>
          <div className='flex-1'>
            <FormField name='dien_giai' disabled={isViewMode} className='w-full' />
          </div>
        </div>

        <div className='flex items-center space-x-4'>
          <Label className='w-32 shrink-0'>Tài khoản nợ</Label>
          <div className='flex-1'>
            <SearchField<AccountModel>
              type='text'
              displayRelatedField='name'
              columnDisplay='code'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              disabled={isViewMode}
              value={taiKhoan?.code || ''}
              relatedFieldValue={taiKhoan?.name || ''}
              onRowSelection={setTaiKhoan}
            />
          </div>
        </div>
      </div>

      <div className='space-y-4'>
        <div>
          <DocumentNumberField
            ma_ct={MA_CHUNG_TU.BAN_HANG.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}
            quyenChungTu={quyenChungTu}
            onQuyenChungTuChange={setQuyenChungTu}
            soChungTu={soChungTu}
            onSoChungTuChange={setSoChungTu}
            disabled={formMode === 'view'}
            classNameSearchField='w-full'
          />
        </div>

        <div className='flex items-center space-x-4'>
          <Label className='w-32 shrink-0'>Ngày chứng từ</Label>
          <div className='flex-1'>
            <FormField name='ngay_ct' type='date' disabled={isViewMode} className='w-full' />
          </div>
        </div>

        <div className='flex items-center space-x-4'>
          <Label className='w-32 shrink-0'>Ngày lập chứng từ</Label>
          <div className='flex-1'>
            <FormField name='ngay_lct' type='date' disabled={isViewMode} className='w-full' />
          </div>
        </div>

        <div className='flex items-center space-x-4'>
          <div className='flex-1'>
            <CurrencyInput formMode={formMode} classNameInput='w-full' />
          </div>
        </div>

        <div className='flex items-center space-x-4'>
          <Label className='w-32 shrink-0'>Trạng thái</Label>
          <div className='flex-1'>
            <FormField
              name='status'
              type='select'
              disabled={isViewMode}
              options={[
                { value: '0', label: 'Chưa ghi sổ' },
                { value: '3', label: 'Chờ duyệt' },
                { value: '5', label: 'Đã ghi sổ' },
                { value: '6', label: 'Đang thực hiện' },
                { value: '7', label: 'Hoàn thành' },
                { value: '9', label: 'Hủy' }
              ]}
              className='w-full'
            />
          </div>
        </div>

        <div className='flex items-center space-x-4'>
          <div className='w-32 shrink-0'></div>
          <div className='flex-1'>
            <FormField
              name='transfer_yn'
              type='checkbox'
              label='Dữ liệu được nhận'
              disabled={isViewMode}
              defaultValue={false}
            />
          </div>
        </div>
      </div>
    </div>
  );

  const tabs: TabItem[] = [
    {
      id: 'info',
      label: 'Thông tin',
      component: InforTab
    }
  ];

  return <AritoHeaderTabs tabs={tabs} />;
}
