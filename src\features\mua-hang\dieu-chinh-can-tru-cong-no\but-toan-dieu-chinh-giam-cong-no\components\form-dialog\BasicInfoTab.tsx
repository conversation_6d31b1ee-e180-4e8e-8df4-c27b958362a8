import { useFormContext } from 'react-hook-form';
import { useCallback } from 'react';
import { FormField, DocumentNumberField, CurrencyInput, SearchField } from '@/components/custom/arito';
import { accountSearchColumns, MA_CHUNG_TU, QUERY_KEYS } from '@/constants';
import { FormFieldState, FormFieldActions } from '../../hooks';
import type { AccountModel } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  const handleExchangeRateChange = useCallback(
    (exchangeRate: number | null) => {
      if (exchangeRate) {
        actions.setTyGia(exchangeRate);
      }
    },
    [actions]
  );

  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-5 lg:space-y-0'>
        {/* Cột trái */}
        <div className='col-span-4 space-y-2'>
          <FormField
            className='flex max-w-[300px] items-center justify-between'
            type='select'
            options={[
              { value: '1', label: '1. Theo hóa đơn' },
              { value: '2', label: '2. Theo khách hàng' }
            ]}
            label='Loại chứng từ'
            name='ma_ngv'
            value={state.loaiChungTu}
            onValueChange={actions.setLoaiChungTu}
            labelClassName='min-w-36 font-medium'
            disabled={formMode === 'view'}
          />
          <div className='flex w-full items-center'>
            <Label className='w-32 min-w-32 font-medium'>Diễn giải</Label>
            <div className='flex-1'>
              <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
            </div>
          </div>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tài khoản có</Label>
            <SearchField<AccountModel>
              type='text'
              disabled={formMode === 'view'}
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              relatedFieldValue={state.taiKhoan?.name || ''}
              onRowSelection={actions.setTaiKhoan}
              value={state.taiKhoan?.code || ''}
            />
          </div>
        </div>

        {/* Cột phải */}
        <div className='col-span-4 grid grid-cols-1 space-y-1 lg:col-span-1'>
          <DocumentNumberField
            ma_ct={MA_CHUNG_TU.MUA_HANG.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO}
            quyenChungTu={state.quyenChungTu}
            onQuyenChungTuChange={actions.setQuyenChungTu}
            soChungTu={state.soChungTu}
            onSoChungTuChange={actions.setSoChungTu}
            formMode={formMode}
            classNameSearchField='w-40'
            labelClassName='font-medium w-36'
          />
          <FormField
            label='Ngày chứng từ'
            type='date'
            labelClassName='min-w-36 font-medium'
            name='ngay_ct'
            disabled={formMode === 'view'}
          />
          <FormField
            label='Ngày lập chứng từ'
            name='ngay_lct'
            type='date'
            labelClassName='min-w-36 font-medium'
            disabled={formMode === 'view'}
          />
          <div className='flex items-center'>
            <CurrencyInput
              formMode={formMode}
              onExchangeRateChange={handleExchangeRateChange}
              labelClassName='font-medium w-36'
            />
          </div>
          <FormField
            label='Trạng thái'
            className='flex max-w-[300px] items-center justify-between'
            name='status'
            type='select'
            labelClassName='min-w-36 font-medium'
            disabled={formMode === 'view'}
            options={[
              { value: '0', label: 'Đã ghi sổ' },
              { value: '3', label: 'Chờ duyệt' },
              { value: '5', label: 'Chưa ghi sổ' }
            ]}
          />
          <FormField
            label='Dữ liệu được nhận'
            labelClassName='font-medium w-32'
            className='mt-3 grid grid-cols-[200px,1fr] items-center'
            name='transfer_yn'
            type='checkbox'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  );
}
