import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hanThanhToanSearchColumns,
  hopDongSearchColumns,
  khachHangSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  mauSoHoaDonSearchColumns,
  phiSearchColumns,
  thueSearchColumns,
  tinhChatThueSearchColumns,
  vatTu1SearchColumns,
  vuViecSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  KheUoc,
  Phi,
  VatTu,
  VuViec,
  HanThanhToan,
  KhachHang,
  type TinhChatThue,
  Tax,
  HopDong,
  DotThanhToan,
  ChiPhiKhongHopLeData
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

const maTCOptions = [
  {
    label:
      '1. Hàng hóa, dịch vụ dùng riêng cho SXKD chịu thuế GTGT và sử dụng cho các hoạt động cung cấp hàng hoá, dịch vụ không kê khai, nộp thuế GTGT đủ điều kiện',
    value: '1'
  },
  { label: '2. Hàng hóa, dịch vụ dùng chung cho SXKD chịu thuế GTGT và không chịu thuế GTGT', value: '2' },
  { label: '3. Hàng hóa, dịch vụ dùng cho dự án đầu tư đủ điều kiện khấu trừ thuế', value: '3' },
  { label: '4. Hàng hóa, dịch vụ không đủ điều kiện khấu trừ', value: '4' },
  { label: '5. Hàng hóa, dịch vụ không phải tổng hợp trên tờ khai 01/GTGT', value: '5' }
];

export const getDataTableColumns = (
  taxRates: any[],
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'so_ct0',
    headerName: 'Số hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='so_ct0'
        type='text'
        value={params.row.so_ct0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
      />
    )
  },
  {
    field: 'so_ct2',
    headerName: 'Ký hiệu',
    width: 100,
    renderCell: params => (
      <CellField
        name='so_ct2'
        type='text'
        value={params.row.so_ct2}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct2', newValue)}
      />
    )
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hóa đơn',
    width: 150,
    renderCell: params => (
      <CellField
        name='ngay_ct0'
        type='date'
        value={params.row.ngay_ct0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
      />
    )
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 150,
    renderCell: params => (
      <SearchField<Tax>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.THUE}/`}
        searchColumns={thueSearchColumns}
        columnDisplay='ma_thue'
        dialogTitle='Danh mục tính chất thuế'
        value={params.row.ma_thue_data?.ten_thue}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_thue_data', row)}
      />
    )
  },
  {
    field: 'ma_mau_ct',
    headerName: 'Mẫu hóa đơn',
    width: 150,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.MAU_SO_HOA_DON}/`}
        searchColumns={mauSoHoaDonSearchColumns}
        columnDisplay='ma_mau_so'
        dialogTitle='Danh mục mẫu chứng từ'
        value={params.row.ma_mau_ct_data?.ma_mau_so || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_mau_ct_data', row)}
      />
    )
  },
  {
    field: 'ma_mau_bc',
    headerName: 'Mẫu báo cáo',
    width: 150,
    renderCell: params => (
      <CellField
        name='mau_bao_cao'
        type='select'
        value={params.row.mau_bao_cao || maTCOptions[0]?.value}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'mau_bao_cao', newValue)}
        options={maTCOptions}
      />
    )
  },
  {
    field: 'ma_tc_thue',
    headerName: 'Mã tính chất',
    width: 150,
    renderCell: params => (
      <SearchField<TinhChatThue>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TINH_CHAT_THUE}/`}
        searchColumns={tinhChatThueSearchColumns}
        columnDisplay='ma_tc_thue'
        dialogTitle='Danh mục tính chất thuế'
        value={params.row.ma_tc_thue_data?.ma_tc_thue}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_tc_thue_data', row)}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã ncc',
    width: 180,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.NHA_CUNG_CAP}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục nhà cung cấp'
        value={params.row.ma_kh_data?.customer_code}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh_thue',
    headerName: 'Tên nhà cung cấp',
    width: 200,
    renderCell: params => (
      <CellField name='ten_nha_cung_cap' type='text' value={params.row.ma_kh_data?.customer_name || ''} />
    )
  },
  {
    field: 'dia_chi',
    headerName: 'Địa chỉ',
    width: 250,
    renderCell: params => <CellField name='dia_chi' type='text' value={params.row.ma_kh_data?.address || ''} />
  },
  {
    field: 'ma_so_thue',
    headerName: 'Mã số thuế',
    width: 150,
    renderCell: params => <CellField name='ma_so_thue' type='text' value={params.row.ma_kh_data?.tax_code || ''} />
  },
  {
    field: 'ten_vt_thue',
    headerName: 'Tên hàng hóa - dịch vụ',
    width: 250,
    renderCell: params => (
      <CellField
        name='ten_vt_thue'
        type='text'
        value={params.row.ten_vt_thue}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_vt_thue', newValue)}
      />
    )
  },
  {
    field: 't_tien_nt',
    headerName: 'Tiền hàng VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='t_tien_nt'
        type='number'
        value={params.row.t_tien_nt || 0.0}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 't_tien_nt', newValue)}
      />
    )
  },
  {
    field: 'tk_thue_no',
    headerName: 'Tk thuế',
    width: 150,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_thue_no_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_thue_no_data', row)}
      />
    )
  },
  {
    field: 'tk_du',
    headerName: 'Tk đối ứng',
    width: 150,
    renderCell: params => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_du_data?.code}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_du_data', row)}
      />
    )
  },
  {
    field: 't_thue_nt',
    headerName: 'Thuế VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='t_thue_nt'
        type='number'
        value={params.row.t_thue_nt}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 't_thue_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_kh9',
    headerName: 'Cục thuế',
    width: 150,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh9_data?.customer_code}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh9_data', row)}
      />
    )
  },
  {
    field: 'ma_tt',
    headerName: 'Mã thanh toán',
    width: 180,
    renderCell: params => (
      <SearchField<HanThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
        searchColumns={hanThanhToanSearchColumns}
        columnDisplay='ma_tt'
        dialogTitle='Danh mục thanh toán'
        value={params.row.ma_tt_data?.ma_tt}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_tt_data', row)}
      />
    )
  },
  {
    field: 'ghi_chu',
    headerName: 'Ghi chú',
    width: 200,
    renderCell: params => (
      <CellField
        name='ghi_chu'
        type='text'
        value={params.row.ghi_chu || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ghi_chu', newValue)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 150,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 150,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 150,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },

  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },

  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTu1SearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục vật tư'
        value={params.row.ma_sp_data?.ma_vt}
        onRowSelection={(row: any) => {
          onCellValueChange(params.row.uuid, 'ma_sp_data', row);
          console.log('Row selected:', params.row);
        }}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 120,
    renderCell: params => (
      <SearchField
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx}
        onRowSelection={(row: any) => {
          onCellValueChange(params.row.uuid, 'ma_lsx_data', row);
          console.log('Row selected:', params.row);
        }}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: params => (
      <SearchField<ChiPhiKhongHopLeData>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cp_khl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cpkhl || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
