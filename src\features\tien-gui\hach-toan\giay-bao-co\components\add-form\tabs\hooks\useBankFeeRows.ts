import { GridCellParams } from '@mui/x-data-grid';
import { useState } from 'react';

export interface BankFeeRow {
  uuid?: string | null;
  tien_cp_nt?: number;
  t_thue_nt?: number;
  thue_suat?: number;
  ma_cpnh_data?: any;
}

export interface SelectedCellInfo {
  id: string;
  field: string;
}

export function useBankFeeRows(initialRows: BankFeeRow[] = []) {
  const [rows, setRows] = useState<BankFeeRow[]>(initialRows);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<BankFeeRow | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedCellInfo | null>(null);

  const handleAddRow = () => {
    const newRow = {
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleDeleteRow = () => {
    if (!selectedRowUuid) return;

    const updatedRows = rows.filter(row => row.uuid !== selectedRowUuid);
    setRows(updatedRows);

    if (updatedRows.length > 0) {
      const lastRow = updatedRows[updatedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid || null);
      setSelectedRow(lastRow);
    } else {
      clearSelection();
    }
  };

  const handleCopyRow = () => {
    if (!selectedRow) return;

    const newRow = {
      ...selectedRow,
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handlePasteRow = () => {
    if (!selectedRow) return;

    const newRow = {
      ...selectedRow,
      uuid: String(Math.random())
    };

    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
      if (selectedIndex === -1) return;

      const newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);
      setRows(newRows);
    } else {
      setRows([...rows, newRow]);
    }

    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleMoveRow = (direction: 'up' | 'down') => {
    if (!selectedRowUuid) return;

    const currentIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

    if (newIndex === currentIndex) return;

    const newRows = [...rows];
    const [movedRow] = newRows.splice(currentIndex, 1);
    newRows.splice(newIndex, 0, movedRow);

    setRows(newRows);

    setSelectedRow(movedRow);
  };

  const handleCellValueChange = (rowUuid: string, field: string, newValue: any) => {
    const rowIndex = rows.findIndex(row => row.uuid === rowUuid);
    if (rowIndex === -1) return;

    const updatedRows = [...rows];
    let updatedRow = {
      ...updatedRows[rowIndex],
      [field]: newValue
    };

    if (field === 'tien_cp_nt' || field === 'ma_cpnh_data') {
      const tien_cp_nt = field === 'tien_cp_nt' ? newValue : updatedRow.tien_cp_nt;
      const thue_suat =
        field === 'ma_cpnh_data' ? newValue?.ma_thue_data?.thue_suat : updatedRow.ma_cpnh_data?.ma_thue_data?.thue_suat;

      if (tien_cp_nt && thue_suat) {
        const tienCpNtNumber = Number(tien_cp_nt) || 0;
        const thueSuatNumber = Number(thue_suat) || 0;
        const calculatedThue = (tienCpNtNumber * thueSuatNumber) / 100;

        updatedRow = {
          ...updatedRow,
          t_thue_nt: calculatedThue,
          thue_suat: thueSuatNumber
        };
      }
    }

    updatedRows[rowIndex] = updatedRow;
    setRows(updatedRows);

    if (rowUuid === selectedRowUuid) {
      setSelectedRow(updatedRows[rowIndex]);
    }
  };

  const handleRowClick = (params: { id: string; row: BankFeeRow }) => {
    const rowUuid = params.id || params.row?.uuid;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const handleCellClick = (params: GridCellParams) => {
    const rowUuid = params.id.toString();
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row as BankFeeRow);

    setSelectedCell({
      id: rowUuid,
      field: params.field
    });
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  };
}
