'use client';

import { CircularProgress } from '@mui/material';
import React from 'react';
import { InitialSearchDialog, EditPrintTemplateDialog, ActionBar } from './components';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { useReportData } from './hooks';

export default function InvoiceDebtStatement() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,

    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData,

    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  } = useReportData();

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            className='border-b border-gray-200'
            searchParams={searchParams}
          />

          <div className='relative flex-1 overflow-hidden'>
            {isLoading ? (
              <div className='absolute inset-0 flex items-center justify-center'>
                <CircularProgress />
              </div>
            ) : (
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            )}
          </div>
        </>
      )}
    </div>
  );
}
