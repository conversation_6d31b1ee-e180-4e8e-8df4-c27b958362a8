import { useState, useEffect, useCallback } from 'react';
import { useGiayBaoCo } from '@/hooks';

interface UseGiayBaoCoDetailReturn {
  detail: any[];
  isLoadingDetail: boolean;
  error: string | null;
  fetchDetail: () => Promise<void>;
  clearDetail: () => void;
}

export const useGiayBaoCoDetail = (selectedUuid?: string): UseGiayBaoCoDetailReturn => {
  const [detail, setDetail] = useState<any[]>([]);
  const [isLoadingDetail, setIsLoadingDetail] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { getGiayBaoCoDetail } = useGiayBaoCo();

  const fetchDetail = useCallback(async () => {
    setDetail([]);
    setError(null);

    if (!selectedUuid) {
      setDetail([]);
      return;
    }
    setIsLoadingDetail(true);

    try {
      const detailData = await getGiayBaoCoDetail(selectedUuid);

      if (Array.isArray(detailData)) {
        setDetail(detailData);
      } else if (detailData && typeof detailData === 'object') {
        if ((detailData as any).results && Array.isArray((detailData as any).results)) {
          setDetail((detailData as any).results);
        } else {
          setDetail([]);
        }
      } else {
        setDetail([]);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Không thể tải chi tiết giấy báo có';
      setError(errorMessage);
      setDetail([]);
    } finally {
      setIsLoadingDetail(false);
    }
  }, [selectedUuid, getGiayBaoCoDetail]);

  const clearDetail = useCallback(() => {
    setDetail([]);
    setError(null);
  }, []);

  useEffect(() => {
    if (selectedUuid) {
      fetchDetail();
    } else {
      setDetail([]);
      setError(null);
    }
  }, [selectedUuid]);

  return {
    detail,
    isLoadingDetail,
    error,
    fetchDetail,
    clearDetail
  };
};
