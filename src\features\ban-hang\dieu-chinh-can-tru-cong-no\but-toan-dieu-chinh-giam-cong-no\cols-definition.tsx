import { GridColDef } from '@mui/x-data-grid';
import { format } from 'date-fns';
import { calculateTotalAmount, calculateTienVnd } from './utils/calculations';

// Main table
export const getDataTableColumns = (handleViewClick?: () => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.row.status;
      switch (status) {
        case '0':
          return 'Chưa ghi sổ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi sổ';
        default:
          return 'Khác';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số chứng từ',
    width: 120,
    renderCell: params => (
      <button onClick={handleViewClick} className='hover:text-blue-500 hover:underline'>
        {params.row.so_ct}
      </button>
    )
  },
  {
    field: 'ngay_ct',
    headerName: '<PERSON>ày c/từ',
    width: 120,
    renderCell: params => (params.row.ngay_ct ? format(new Date(params.row.ngay_ct), 'dd/MM/yyyy') : '')
  },
  {
    field: 'ma_kh_data',
    headerName: 'Mã khách hàng',
    width: 150,
    renderCell: params => {
      // Try to get customer info from main object first, then from first detail item
      const mainCustomer = params.row.ma_kh_data?.customer_code || params.row.ma_kh;
      const detailCustomer = params.row.chi_tiet_data?.[0]?.ma_kh_data?.customer_code;
      return mainCustomer || detailCustomer || '';
    }
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => {
      // Try to get customer name from main object first, then from first detail item
      const mainCustomerName = params.row.ma_kh_data?.customer_name;
      const detailCustomerName = params.row.chi_tiet_data?.[0]?.ma_kh_data?.customer_name;
      return mainCustomerName || detailCustomerName || '';
    }
  },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
  {
    field: 'tk_no',
    headerName: 'Tk nợ',
    width: 100,
    renderCell: params => params.row.tk_data?.code || params.row.tk || ''
  },
  {
    field: 't_tien_nt',
    headerName: 'Tổng tiền',
    width: 150,
    renderCell: params => {
      // Try to calculate from detail data first, fallback to stored value
      let tien = params.row.t_tien_nt;

      // If we have detail data, calculate the total dynamically
      if (params.row.chi_tiet_data && Array.isArray(params.row.chi_tiet_data) && params.row.chi_tiet_data.length > 0) {
        const calculatedTotal = calculateTotalAmount(params.row.chi_tiet_data, params.row.ma_ngv || '1');
        if (calculatedTotal !== undefined && calculatedTotal !== null) {
          tien = calculatedTotal;
        }
      }

      return tien ? tien.toLocaleString('vi-VN') : '0';
    }
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 100,
    renderCell: params => params.row.ma_nt_data?.ma_nt || params.row.ma_nt || 'VND'
  },
  {
    field: 'ma_ngv',
    headerName: 'Loại phiếu thu',
    width: 150,
    renderCell: params => {
      const maNgv = params.row.ma_ngv;
      switch (maNgv) {
        case '1':
          return 'Theo hóa đơn';
        case '2':
          return 'Theo khách hàng';
        default:
          return '';
      }
    }
  }
];

export const getInputTableColumns = (ma_ngv?: string): GridColDef[] => [
  {
    field: 'ma_kh_data',
    headerName: 'Mã khách hàng',
    width: 150,
    renderCell: params => params.row.ma_kh_data?.customer_code || params.row.ma_kh || ''
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name || ''
  },
  {
    field: 'id_hd_data',
    headerName: 'Hóa đơn',
    width: 120,
    renderCell: params => params.row.id_hd_data?.so_ct || params.row.id_hd || ''
  },
  {
    field: 'ngay_hd',
    headerName: 'Ngày hóa đơn',
    width: 120,
    renderCell: params => {
      const ngayHd = params.row.id_hd_data?.ngay_ct;
      return ngayHd ? format(new Date(ngayHd), 'dd/MM/yyyy') : '';
    }
  },
  {
    field: 'tk_co_data',
    headerName: 'Tài khoản có',
    width: 150,
    renderCell: params => params.row.tk_co_data?.code || params.row.tk_co || ''
  },
  {
    field: 'ten_tk',
    headerName: 'Tên tài khoản',
    width: 200,
    renderCell: params => params.row.tk_co_data?.name || ''
  },
  {
    field: 'ngoai_te',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => params.row.id_hd_data?.ngoai_te || 'VND'
  },
  {
    field: 'ty_gia_hd',
    headerName: 'Tỷ giá hđ',
    width: 120,
    renderCell: params => params.row.ty_gia_hd || '1.00'
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150,
    renderCell: params => {
      // Use the same calculation logic as the form dialog
      const tien = calculateTienVnd(params.row, ma_ngv);
      return tien ? tien.toLocaleString('vi-VN') : '0';
    }
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200,
    renderCell: params => params.row.dien_giai || ''
  }
];
