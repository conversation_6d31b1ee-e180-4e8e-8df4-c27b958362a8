import { GridColDef } from '@mui/x-data-grid';
import { format } from 'date-fns';

export const getDataTableColumns = (handleViewClick: () => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.row.status;
      switch (status) {
        case '0':
          return 'Chưa ghi sổ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi sổ';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số chứng từ',
    width: 120,
    renderCell: params => (
      <button onClick={handleViewClick} className='hover:text-blue-500 hover:underline'>
        {params.row.so_ct}
      </button>
    )
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120,
    renderCell: params => format(params.row.ngay_ct, 'dd/MM/yyyy')
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 150,
    renderCell: params => params.row.chi_tiet_data[0]?.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => params.row.chi_tiet_data[0]?.ma_kh_data?.customer_name
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'tk',
    headerName: 'Tk có',
    width: 150,
    renderCell: params => params.row.tk_data?.code
  },
  {
    field: 't_tien',
    headerName: 'Tổng tiền',
    width: 150,
    renderCell: params => params.row.t_tien
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => params.row.ma_nt_data?.ma_nt
  },
  {
    field: 'ma_ngv',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.row.ma_ngv;
      switch (status) {
        case '1':
          return '1. Theo hóa đơn';
        case '2':
          return '2. Theo khách hàng';
        default:
          return '1. Theo khách hàng';
      }
    }
  }
];

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 150,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  {
    field: 'id_hd',
    headerName: 'Hóa đơn',
    width: 120,
    renderCell: params => params.row.id_hd_data?.so_ct
  },
  {
    field: 'ngay_ct_hd',
    headerName: 'Ngày hóa đơn',
    width: 120
  },
  {
    field: 'tk_no',
    headerName: 'Tài khoản nợ',
    width: 150,
    renderCell: params => params.row.tk_no_data?.code
  },
  {
    field: 'ten_tk',
    headerName: 'Tên tài khoản',
    width: 200,
    renderCell: params => params.row.tk_no_data?.name
  },
  {
    field: 'ma_nt_hd',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => params.row.id_hd_data?.ngoai_te
  },
  {
    field: 'ty_gia_hd',
    headerName: 'Tỷ giá hđ',
    width: 120,
    renderCell: () => '1'
  },
  {
    field: 'tien_tren_hd',
    headerName: 'Tiền trên hóa đơn',
    width: 120,
    renderCell: params => params.row.id_hd_data?.tien_tren_hd
  },
  {
    field: 'tien_con_phai_tt',
    headerName: 'Còn lại',
    width: 120,
    renderCell: params => params.row.id_hd_data?.tien_con_phai_tt
  },
  {
    field: 'tien_hd_nt',
    headerName: 'Tiền VND',
    width: 120,
    renderCell: params => params.row.id_hd_data?.tien_hd_nt
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => params.row.ma_bp_data?.ma_bp
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => params.row.ma_vv_data?.ma_vu_viec
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => params.row.ma_hd_data?.ma_hd
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => params.row.ma_dtt_data?.ma_dtt
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => params.row.ma_ku_data?.ma_ku
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 100,
    renderCell: params => params.row.ma_phi_data?.ma_phi
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_sp_data?.ma_vt
  },
  {
    field: 'lenh_sx',
    headerName: 'Lệnh sản xuất',
    width: 150
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => params.row.ma_cp0_data?.ma_cp
  }
];

export const historyColumns: GridColDef[] = [
  { field: 'username', headerName: 'Tài khoản', width: 100 },
  { field: 'nickname', headerName: 'Tên tài khoản', width: 200 },
  { field: 'action', headerName: 'Hành động', width: 100 },
  { field: 'datetime0', headerName: 'Thời gian', width: 150 }
];
