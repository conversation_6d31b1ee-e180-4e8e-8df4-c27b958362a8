import { z } from 'zod';

export const FormSchema = z.object({
  // Basic Info fields
  ma_ngv: z.string().min(1),
  dien_giai: z.string().min(1),
  ngay_ct: z.string().min(1),
  ngay_lct: z.string().min(1),
  ma_nt: z.string().min(1),
  ty_gia: z.coerce.number().min(1),
  status: z.string().optional(),
  transfer_yn: z.boolean().optional(),

  // Exchange Rate Tab fields
  tg_dd: z.boolean().optional(),

  // Other Tab fields
  ma_kh: z.string().nullable().optional(),
  so_ct_goc: z.coerce.number().optional(),
  dien_giai_ct_goc: z.string().optional()
});

export type FormValues = z.infer<typeof FormSchema>;

export const initialFormValues: FormValues = {
  ma_ngv: '1',
  dien_giai: '',
  ngay_ct: new Date().toISOString().split('T')[0],
  ngay_lct: new Date().toISOString().split('T')[0],
  ma_nt: '',
  ty_gia: 0,
  status: '5',
  transfer_yn: false,
  tg_dd: false,
  ma_kh: '',
  so_ct_goc: 0,
  dien_giai_ct_goc: ''
};
