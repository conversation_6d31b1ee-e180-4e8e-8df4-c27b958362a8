/**
 * Transform search filters for API request
 * @param filters - Filter data from the form
 * @returns Transformed filters ready for API request
 */
export const transformSearchFilters = (filters: any) => {
  return {
    ...filters,
    // The ma_unit should already contain the UUID from the FormField selection
    // If it's empty or undefined, don't include it in the request
    ma_unit: filters.ma_unit || undefined
  };
};
