import { formatMoney, formatNumber } from '@/lib/formatUtils';
import { FormField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface BottomBarProps {
  tong_tien?: number;
  tong_thue?: number;
  tong_phi?: number;
  tong_thanh_toan?: number;
}

export function BottomBar({ tong_tien = 0, tong_thue = 0, tong_phi = 0, tong_thanh_toan = 0 }: BottomBarProps) {
  return (
    <div className='w-full border-t bg-white px-4 py-2'>
      <div className='flex'>
        <div className='ml-4 flex flex-col'>
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng tiền</Label>
            <FormField
              name='t_tien_nt'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(tong_tien)}
            />
          </div>
          {/* Column 3 */}
          <div className='flex items-center'>
            <Label className='w-32 font-medium'>Tổng thuế</Label>
            <FormField
              name='t_thue_nt'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(tong_thue)}
            />
          </div>
        </div>

        <div className='ml-auto flex w-1/3 flex-col'>
          <div className='flex items-center'>
            <Label className='w-40 font-medium'>Tổng phí ngân hàng</Label>
            <FormField
              name='t_cp_nt'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(tong_phi)}
            />
          </div>
          {/* Column 4 */}
          <div className='flex items-center'>
            <Label className='w-40 font-medium'>Tổng thanh toán</Label>
            <FormField
              name='t_tt_nt'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(tong_thanh_toan)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
