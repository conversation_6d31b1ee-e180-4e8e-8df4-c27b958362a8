import { GridCellParams } from '@mui/x-data-grid';
import { useState } from 'react';

export interface InputTableRow {
  uuid?: string | null;
  tien_nt?: number;
  t_tien_nt?: number;
  t_thue_nt?: number;
  tien_cp_nt?: number;
  id_hd?: any;
  id_hd_data?: {
    tien_tren_hd?: number;
    [key: string]: any;
  };
  ma_thue_data?: {
    thue_suat?: number;
    tax_rate?: number;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface SelectedCellInfo {
  id: string;
  field: string;
}

export function useInputTableRows(initialRows: InputTableRow[] = []) {
  const [rows, setRows] = useState<InputTableRow[]>(initialRows);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<InputTableRow | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedCellInfo | null>(null);

  const handleAddRow = () => {
    const newRow = {
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    // Select the newly added row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleDeleteRow = () => {
    let updatedRows: InputTableRow[] = [];

    if (selectedRowUuid) {
      updatedRows = rows.filter(row => row.uuid !== selectedRowUuid);
      setRows(updatedRows);
    } else if (rows.length > 0) {
      updatedRows = rows.slice(0, -1);
      setRows(updatedRows);
    }

    // If there are remaining rows, select the last one
    if (updatedRows.length > 0) {
      const lastRow = updatedRows[updatedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid || null);
      setSelectedRow(lastRow);
    } else {
      // If no rows remain, clear selection
      clearSelection();
    }
  };

  const handleCopyRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    const newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    // Select the newly copied row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handlePasteRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    let newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
      if (selectedIndex === -1) return;

      const newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);

      setRows(newRows);
    } else {
      setRows([...rows, newRow]);
    }

    // Select the newly pasted row
    setSelectedRowUuid(newRow.uuid);
    setSelectedRow(newRow);
  };

  const handleMoveRow = (direction: 'up' | 'down') => {
    if (!selectedRowUuid) return;

    const currentIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

    if (newIndex === currentIndex) return;

    const newRows = [...rows];
    const [movedRow] = newRows.splice(currentIndex, 1);
    newRows.splice(newIndex, 0, movedRow);

    setRows(newRows);

    // Ensure selection follows the moved row
    // No need to update selectedRowUuid as it remains the same
    setSelectedRow(movedRow);
  };

  const handleCellValueChange = (rowUuid: string, field: string, newValue: any) => {
    const rowIndex = rows.findIndex(row => row.uuid === rowUuid);
    if (rowIndex === -1) return;

    const updatedRows = [...rows];
    updatedRows[rowIndex] = {
      ...updatedRows[rowIndex],
      [field]: newValue
    };

    if (field === 'id_hd_data' && newValue?.tien_tren_hd) {
      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        tien_nt: newValue.tien_tren_hd
      };
    }

    if (field === 'ma_thue_data' && newValue) {
      const thue_suat = Number(newValue.thue_suat || newValue.tax_rate) || 0;

      const t_tien_nt = Number(updatedRows[rowIndex].t_tien_nt) || 0;
      const tien_cp_nt = Number(updatedRows[rowIndex].tien_cp_nt) || 0;

      // Calculate tax from t_tien_nt
      const thue_t_tien = t_tien_nt > 0 ? (t_tien_nt * thue_suat) / 100 : 0;

      // Calculate tax from tien_cp_nt
      const thue_tien_cp = tien_cp_nt > 0 ? (tien_cp_nt * thue_suat) / 100 : 0;

      // Total tax is sum of both
      const t_thue_nt = thue_t_tien + thue_tien_cp;

      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        t_thue_nt: t_thue_nt
      };
    }

    if (field === 't_tien_nt') {
      const ma_thue_data = updatedRows[rowIndex].ma_thue_data;
      if (ma_thue_data) {
        const thue_suat = Number(ma_thue_data.thue_suat || ma_thue_data.tax_rate) || 0;
        const t_tien_nt = Number(newValue) || 0;
        const tien_cp_nt = Number(updatedRows[rowIndex].tien_cp_nt) || 0;

        // Calculate tax from t_tien_nt
        const thue_t_tien = (t_tien_nt * thue_suat) / 100;

        // Calculate tax from existing tien_cp_nt
        const thue_tien_cp = tien_cp_nt > 0 ? (tien_cp_nt * thue_suat) / 100 : 0;

        // Total tax is sum of both
        const t_thue_nt = thue_t_tien + thue_tien_cp;

        updatedRows[rowIndex] = {
          ...updatedRows[rowIndex],
          t_thue_nt: t_thue_nt
        };
      }
    }

    if (field === 'tien_cp_nt') {
      const ma_thue_data = updatedRows[rowIndex].ma_thue_data;
      const tien_cp_nt = Number(newValue) || 0;

      // Always update tien_cp_nt value
      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        tien_cp_nt: tien_cp_nt
      };

      // Calculate tax if ma_thue_data exists
      if (ma_thue_data) {
        const thue_suat = Number(ma_thue_data.thue_suat || ma_thue_data.tax_rate) || 0;
        const t_tien_nt = Number(updatedRows[rowIndex].t_tien_nt) || 0;

        // Calculate tax from existing t_tien_nt
        const thue_t_tien = t_tien_nt > 0 ? (t_tien_nt * thue_suat) / 100 : 0;

        // Calculate tax from tien_cp_nt
        const thue_tien_cp = (tien_cp_nt * thue_suat) / 100;

        // Total tax is sum of both
        const t_thue_nt_total = thue_t_tien + thue_tien_cp;

        updatedRows[rowIndex] = {
          ...updatedRows[rowIndex],
          t_thue_nt: t_thue_nt_total
        };
      } else {
        // If no tax data, keep existing t_thue_nt from t_tien_nt if any
        const t_tien_nt = Number(updatedRows[rowIndex].t_tien_nt) || 0;
        if (t_tien_nt === 0) {
          updatedRows[rowIndex] = {
            ...updatedRows[rowIndex],
            t_thue_nt: 0
          };
        }
      }
    }

    setRows(updatedRows);

    // If the changed row is the currently selected row, update selectedRow
    if (rowUuid === selectedRowUuid) {
      setSelectedRow(updatedRows[rowIndex]);
    }
  };

  const handleRowClick = (params: { id: string; row: InputTableRow }) => {
    const rowUuid = params.id || params.row?.uuid;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const handleCellClick = (params: GridCellParams) => {
    const rowUuid = params.id.toString();
    if (!rowUuid) return;

    // Update row selection
    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row as InputTableRow);

    // Update cell selection
    setSelectedCell({
      id: rowUuid,
      field: params.field
    });
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  };
}
