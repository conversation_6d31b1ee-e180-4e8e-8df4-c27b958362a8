import { useFormContext } from 'react-hook-form';
import { customerGroupSearchColumns, customerSearchColumns, regionSearchColumns } from './cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { nhomColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';

interface DetailsTabProps {
  searchFieldStates?: any;
}

const DetailsTab: React.FC<DetailsTabProps> = ({ searchFieldStates }) => {
  const { setValue } = useFormContext();

  return (
    <div className='min-h-[390px] w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* 1. Mã khách hàng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã khách hàng:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
              searchColumns={customerSearchColumns}
              dialogTitle='Danh mục khách hàng'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              value={searchFieldStates?.customer?.customer_code || ''}
              relatedFieldValue={searchFieldStates?.customer?.customer_name || ''}
              onValueChange={value => {
                setValue('customerCode', value);
              }}
              onRowSelection={(row: any) => {
                setValue('customerCode', row.customer_code);
                searchFieldStates?.setCustomer(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* 2. Nhóm khách hàng */}
        <div className='flex w-[76%] items-center'>
          <Label className='w-40 min-w-40 text-left'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='grid grid-cols-3'>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=NCC1`}
                  searchColumns={nhomColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='ma_nhom'
                  value={searchFieldStates?.customerGroup1?.customer_group_code || ''}
                  relatedFieldValue={searchFieldStates?.customerGroup1?.customer_group_name || ''}
                  onRowSelection={(row: any) => {
                    searchFieldStates?.setCustomerGroup1(row);
                  }}
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=NCC2`}
                  searchColumns={nhomColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='ma_nhom'
                  value={searchFieldStates?.customerGroup2?.customer_group_code || ''}
                  relatedFieldValue={searchFieldStates?.customerGroup2?.customer_group_name || ''}
                  onRowSelection={(row: any) => {
                    searchFieldStates?.setCustomerGroup2(row);
                  }}
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=NCC3`}
                  searchColumns={nhomColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='ma_nhom'
                  value={searchFieldStates?.customerGroup3?.customer_group_code || ''}
                  relatedFieldValue={searchFieldStates?.customerGroup3?.customer_group_name || ''}
                  onRowSelection={(row: any) => {
                    searchFieldStates?.setCustomerGroup3(row);
                  }}
                  className='w-full'
                />
              </div>
            </div>
          </div>
        </div>

        {/* 3. Khu vực */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Khu vực:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHU_VUC}`}
              searchColumns={regionSearchColumns}
              dialogTitle='Khu vực'
              columnDisplay='rg_code'
              displayRelatedField='rgname'
              value={searchFieldStates?.region?.rg_code || ''}
              relatedFieldValue={searchFieldStates?.region?.rgname || ''}
              onValueChange={value => {
                setValue('region', value);
              }}
              onRowSelection={(row: any) => {
                setValue('region', row.rg_code);
                searchFieldStates?.setRegion(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* 4. Chi tiết */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Chi tiết:</Label>
          <div>
            <FormField
              name='ct_theo'
              type='select'
              options={[
                { value: 1, label: 'Theo hóa đơn' },
                { value: 2, label: 'Theo khách hàng' }
              ]}
            />
          </div>
        </div>

        {/* 5. Số dư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số dư:</Label>
          <div>
            <FormField
              name='tt_yn'
              type='select'
              options={[
                { value: '0', label: 'Tất cả' },
                { value: '1', label: 'Chỉ có hóa đơn số dư lớn hơn 0' }
              ]}
            />
          </div>
        </div>

        {/* 6. Số ngày hạn tt */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số ngày hạn tt:</Label>
          <div className='w-48'>
            <FormField name='so_ngay_tt' type='number' defaultValue={30} />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số ngày cảnh báo:</Label>
          <div className='w-48'>
            <FormField name='so_ngay_cb' type='number' defaultValue={0} />
          </div>
        </div>

        {/* 7. Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
          <div className='w-3/4 flex-1'>
            <FormField
              name='mau_bc'
              type='select'
              options={[
                { value: 20, label: 'Mẫu tiền chuẩn' },
                { value: 30, label: 'Mẫu ngoại tệ' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
