import { GridColDef } from '@mui/x-data-grid';
import { formatDate, formatMoney } from '@/lib/formatUtils';

const isFirstRow = (params: any) => params.row.stt === '' || params.row.stt === 0;

export const getDataTableColumns = (): GridColDef[] => [
  {
    field: 'stt',
    headerName: 'STT',
    width: 60,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 110,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatDate(params.value)}</span>;
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày hóa đơn',
    width: 120,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatDate(params.value)}</span>;
    }
  },
  {
    field: 'so_ct0',
    headerName: 'Số hóa đơn',
    width: 130,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 300,
    flex: 1,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 130,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 250,
    flex: 1,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ma_nvbh',
    headerName: 'Mã NVBH',
    width: 100,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ten_nvbh',
    headerName: 'Tên NVBH',
    width: 180,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 't_tt',
    headerName: 'Tổng tiền HĐ',
    width: 150,
    type: 'number',
    renderCell: params => {
      return (
        <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatMoney(params.value || 0)}</span>
      );
    }
  },
  {
    field: 'tt',
    headerName: 'Đã thu',
    width: 130,
    type: 'number',
    renderCell: params => {
      return (
        <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatMoney(params.value || 0)}</span>
      );
    }
  },
  {
    field: 'cl',
    headerName: 'Còn lại',
    width: 130,
    type: 'number',
    renderCell: params => {
      return (
        <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatMoney(params.value || 0)}</span>
      );
    }
  },
  {
    field: 'trong_han',
    headerName: 'Trong hạn',
    width: 130,
    type: 'number',
    renderCell: params => {
      return (
        <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatMoney(params.value || 0)}</span>
      );
    }
  },
  {
    field: 'qua_han01',
    headerName: 'Quá hạn 1-45 ngày',
    width: 150,
    type: 'number',
    renderCell: params => {
      return (
        <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatMoney(params.value || 0)}</span>
      );
    }
  },
  {
    field: 'qua_han02',
    headerName: 'Quá hạn 46-90 ngày',
    width: 160,
    type: 'number',
    renderCell: params => {
      return (
        <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatMoney(params.value || 0)}</span>
      );
    }
  },
  {
    field: 'qua_han03',
    headerName: 'Quá hạn 91-135 ngày',
    width: 170,
    type: 'number',
    renderCell: params => {
      return (
        <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatMoney(params.value || 0)}</span>
      );
    }
  },
  {
    field: 'qua_han04',
    headerName: 'Quá hạn 136-180 ngày',
    width: 180,
    type: 'number',
    renderCell: params => {
      return (
        <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatMoney(params.value || 0)}</span>
      );
    }
  },
  {
    field: 'qua_han05',
    headerName: 'Quá hạn >181 ngày',
    width: 160,
    type: 'number',
    renderCell: params => {
      return (
        <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatMoney(params.value || 0)}</span>
      );
    }
  },
  {
    field: 'ngay_dh0',
    headerName: 'Ngày đến hạn',
    width: 130,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatDate(params.value)}</span>;
    }
  },
  {
    field: 'han_tt',
    headerName: 'Hạn thanh toán',
    width: 130,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{formatDate(params.value)}</span>;
    }
  },
  {
    field: 'so_ngay',
    headerName: 'Số ngày',
    width: 100,
    type: 'number',
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ma_nt',
    headerName: 'Loại tiền',
    width: 100,
    renderCell: params => {
      return <span style={{ fontWeight: isFirstRow(params) ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  }
];
