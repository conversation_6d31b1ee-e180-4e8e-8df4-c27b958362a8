'use client';

import { useState, useEffect, useMemo } from 'react';
import { getFormTitle, getFormActionButtons, calculateTotals, transformFormData } from '../../utils';
import { AritoHeaderTabs, AritoForm, LoadingOverlay } from '@/components/custom/arito';
import { useInputTableRows, useLoading, useSearchFieldStates } from '../../hooks';
import { formSchema, initialFormValues } from '../../schema';
import { useAuth } from '@/contexts/auth-context';
import { PhiNganHangTab } from './PhiNganHangTab';
import { ConfirmDialog } from '../../components';
import { BasicInfoTab } from './BasicInfoTab';
import { ThanhToanTab } from './ThanhToanTab';
import UyNhiemChiTab from './UyNhiemChiTab';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { TyGiaTab } from './TyGiaTab';
import { OtherTab } from './OtherTab';
import { ThueTab } from './ThueTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const { entityUnit } = useAuth();
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const { loading } = useLoading({
    isOpen: open,
    external: false,
    duration: 500
  });
  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useInputTableRows(initialData?.chi_tiet_data || []);
  const {
    rows: thueRows,
    selectedRowUuid: thueSelectedRowUuid,
    handleRowClick: thueHandleRowClick,
    handleAddRow: thueHandleAddRow,
    handleDeleteRow: thueHandleDeleteRow,
    handleCopyRow: thueHandleCopyRow,
    handlePasteRow: thueHandlePasteRow,
    handleMoveRow: thueHandleMoveRow,
    handleCellValueChange: thueHandleCellValueChange
  } = useInputTableRows(initialData?.thue_data || []);
  const {
    rows: phiNganHangRows,
    selectedRowUuid: phiNganHangSelectedRowUuid,
    handleRowClick: phiNganHangHandleRowClick,
    handleAddRow: phiNganHangHandleAddRow,
    handleDeleteRow: phiNganHangHandleDeleteRow,
    handleCopyRow: phiNganHangHandleCopyRow,
    handlePasteRow: phiNganHangHandlePasteRow,
    handleMoveRow: phiNganHangHandleMoveRow,
    handleCellValueChange: phiNganHangHandleCellValueChange
  } = useInputTableRows(initialData?.phi_ngan_hang_data || []);
  const { state, actions } = useSearchFieldStates(initialData);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const { tong_phi, tong_thue, tong_tien, tong_thanh_toan } = useMemo(() => {
    return calculateTotals(detailRows, thueRows, phiNganHangRows);
  }, [detailRows, thueRows, phiNganHangRows]);

  const validateFormData = () => {
    const errors: string[] = [];

    // Validate document number for add mode
    if (formMode === 'add' && !state.soChungTu) {
      errors.push('Số chứng từ là bắt buộc');
    }

    // Validate required state fields
    if (!state.quyenChungTu && formMode === 'add') {
      errors.push('Quyển chứng từ là bắt buộc');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const handleSubmit = (data: any) => {
    // Validate form data
    const validation = validateFormData();
    if (!validation.isValid) {
      // Show first error message
      console.error('Form validation failed:', validation.errors);
      // You can replace this with a toast notification if available
      alert(validation.errors[0]);
      return;
    }

    const formData = transformFormData(
      data,
      state,
      { tong_phi, tong_thue, tong_tien, tong_thanh_toan },
      entityUnit,
      detailRows,
      thueRows,
      phiNganHangRows,
      formMode
    );

    console.log('formData: ', formData);

    if (formMode === 'edit') {
      onSubmit?.({ ...formData, i_so_ct: initialData?.i_so_ct, so_ct: initialData?.so_ct });
    } else {
      onSubmit?.({ ...formData, ngay_lct: new Date().toISOString().split('T')[0] });
    }

    setIsFormDirty(false);
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  return (
    <>
      <div className='relative overflow-auto'>
        {loading && (
          <div className='absolute inset-0 z-50 flex items-center justify-center bg-white bg-opacity-80'>
            <LoadingOverlay />
          </div>
        )}
        <AritoForm
          mode={formMode}
          initialData={initialData || initialFormValues}
          title={title}
          actionButtons={actionButtons}
          subTitle='Giấy báo nợ'
          onSubmit={handleSubmit}
          onClose={handleClose}
          schema={formSchema}
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto' onChange={() => setIsFormDirty(true)}>
              <AritoHeaderTabs
                tabs={[
                  {
                    id: 'info',
                    label: 'Thông tin',
                    component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                  },
                  ...(formMode === 'view'
                    ? [
                        {
                          id: 'history',
                          label: 'Lịch sử',
                          component: <HistoryTab />
                        }
                      ]
                    : [])
                ]}
                onTabChange={handleTabChange}
                defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
              />
            </div>
          }
          tabs={
            activeTab === 'info' && [
              {
                id: 'details',
                label: 'Chi tiết',
                component: (
                  <DetailTab
                    formMode={formMode}
                    rows={detailRows}
                    selectedRowUuid={detailSelectedRowUuid}
                    onRowClick={detailHandleRowClick}
                    onAddRow={detailHandleAddRow}
                    onDeleteRow={detailHandleDeleteRow}
                    onCopyRow={detailHandleCopyRow}
                    onPasteRow={detailHandlePasteRow}
                    onMoveRow={detailHandleMoveRow}
                    onCellValueChange={detailHandleCellValueChange}
                    status={state.loaiChungTu}
                    loaiHD={state.loai_hd}
                    setNhomLoaiHD={actions.setNhomLoaiHD}
                  />
                )
              },
              {
                id: 'thue',
                label: 'Thuế',
                component: (
                  <ThueTab
                    formMode={formMode}
                    rows={thueRows}
                    selectedRowUuid={thueSelectedRowUuid}
                    onRowClick={thueHandleRowClick}
                    onAddRow={thueHandleAddRow}
                    onDeleteRow={thueHandleDeleteRow}
                    onCopyRow={thueHandleCopyRow}
                    onPasteRow={thueHandlePasteRow}
                    onMoveRow={thueHandleMoveRow}
                    onCellValueChange={thueHandleCellValueChange}
                  />
                )
              },
              {
                id: 'thong_tin_thanh_toan',
                label: 'Thông tin thanh toán',
                component: <ThanhToanTab formMode={formMode} hanTT={state.hanTT} setHanTT={actions.setHanTT} />
              },
              {
                id: 'ty_gia',
                label: 'Tỷ giá',
                component: <TyGiaTab formMode={formMode} />
              },
              {
                id: 'phi_nganHang',
                label: 'Phí ngân hàng',
                component: (
                  <PhiNganHangTab
                    formMode={formMode}
                    rows={phiNganHangRows}
                    selectedRowUuid={phiNganHangSelectedRowUuid}
                    onRowClick={phiNganHangHandleRowClick}
                    onAddRow={phiNganHangHandleAddRow}
                    onDeleteRow={phiNganHangHandleDeleteRow}
                    onCopyRow={phiNganHangHandleCopyRow}
                    onPasteRow={phiNganHangHandlePasteRow}
                    onMoveRow={phiNganHangHandleMoveRow}
                    onCellValueChange={phiNganHangHandleCellValueChange}
                  />
                )
              },
              {
                id: 'uy_nhiem_chi',
                label: 'Ủy nhiệm chi',
                component: <UyNhiemChiTab formMode={formMode} state={state} actions={actions} />
              },
              {
                id: 'khac',
                label: 'Khác',
                component: <OtherTab formMode={formMode} initialData={initialData} />
              }
            ]
          }
          classNameBottomBar='relative w-full flex justify-end gap-2'
          bottomBar={
            activeTab === 'info' && (
              <BottomBar
                tong_tien={tong_tien}
                tong_thue={tong_thue}
                tong_phi={tong_phi}
                tong_thanh_toan={tong_thanh_toan}
              />
            )
          }
        />
      </div>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
