import { useState, useEffect } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { useBangKeCongNoTheoHoaDon } from './useBangKeCongNoTheoHoaDon';
import { BangKeCongNoTheoHoaDonItem } from '@/types/schemas';
import { getDataTableColumns } from '../cols-definition';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
  loading: boolean;
}

export function useTableData(searchParams: any, shouldFetch: boolean = false): UseTableDataReturn {
  const [tables, setTables] = useState<TableData[]>([]);
  const { data, isLoading, fetchData } = useBangKeCongNoTheoHoaDon(searchParams || {});

  useEffect(() => {
    if (!searchParams || !shouldFetch) return;
    fetchData(searchParams);
  }, [searchParams, shouldFetch, fetchData]);

  useEffect(() => {
    if (data && data.length > 0) {
      const mappedRows = data.map((item: BangKeCongNoTheoHoaDonItem, index: number) => ({
        id: item.id || `row-${index}`,

        stt: item.stt || (index === 0 ? '' : index),
        ngay_ct: item.ngay_ct || '',
        so_ct: item.so_ct || '',
        ngay_ct0: item.ngay_ct0 || '',
        so_ct0: item.so_ct0 || '',
        ma_kh: item.ma_kh || '',
        ten_kh: item.ten_kh || '',
        ma_nvbh: item.ma_nvbh || '',
        ten_nvbh: item.ten_nvbh || '',
        t_tt: item.t_tt || 0,
        tt: item.tt || 0,
        cl: item.cl || 0,
        trong_han: item.trong_han || 0,
        qua_han01: item.qua_han01 || 0,
        qua_han02: item.qua_han02 || 0,
        qua_han03: item.qua_han03 || 0,
        qua_han04: item.qua_han04 || 0,
        qua_han05: item.qua_han05 || 0,
        ngay_dh0: item.ngay_dh0 || '',
        han_tt: item.han_tt || '',
        so_ngay: item.so_ngay || 0,
        dien_giai: item.dien_giai || '',
        ma_nt: item.ma_nt || '',
        ma_unit: item.ma_unit || '',

        id_tt: item.id_tt || '',
        id_ct: item.id_ct || '',
        loai_tt: item.loai_tt || '',
        unit_id: item.unit_id || '',
        ma_ct: item.ma_ct || '',
        ma_tt: item.ma_tt || '',
        ty_gia: item.ty_gia || 1,
        t_tt_nt0: item.t_tt_nt0 || 0,
        t_tt0: item.t_tt0 || 0,
        tt_yn: item.tt_yn || '',
        ngay_tt: item.ngay_tt || ''
      }));

      const tableData: TableData[] = [
        {
          name: 'Bảng kê công nợ theo hóa đơn',
          columns: getDataTableColumns(),
          rows: mappedRows
        }
      ];

      setTables(tableData);
    } else {
      setTables([]);
    }
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    loading: isLoading,
    handleRowClick
  };
}
