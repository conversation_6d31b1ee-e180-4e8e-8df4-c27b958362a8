import { useState, useCallback } from 'react';
import { transformApiResponse } from '../utils/transform-data';
import { BangKeCongNoTheoHoaDonItem } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

export interface BangKeCongNoTheoHoaDonResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangKeCongNoTheoHoaDonItem[];
  summary?: any;
}

export interface UseBangKeCongNoTheoHoaDonReturn {
  data: BangKeCongNoTheoHoaDonItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
  submitBangKeCongNoTheoHoaDon: (requestBody: any) => Promise<void>;
}

export const useBangKeCongNoTheoHoaDon = (initialParams: any = {}): UseBangKeCongNoTheoHoaDonReturn => {
  const [data, setData] = useState<BangKeCongNoTheoHoaDonItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastSearchParams, setLastSearchParams] = useState<any>(initialParams);

  const { entity } = useAuth();

  const fetchData = useCallback(
    async (searchParams: any) => {
      if (!entity?.slug) {
        const error = new Error('Entity not found');
        setError(error);
        throw error;
      }

      setIsLoading(true);
      setError(null);
      setLastSearchParams(searchParams);

      try {
        const response = await api.post(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.BANG_KE_CONG_NO_THEO_HOA_DON}/`,
          searchParams
        );

        const transformedData = transformApiResponse(response.data);
        setData(transformedData.results);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching data';
        const error = new Error(errorMessage);
        setError(error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  const refreshData = useCallback(async () => {
    if (lastSearchParams) {
      await fetchData(lastSearchParams);
    }
  }, [fetchData, lastSearchParams]);

  const submitBangKeCongNoTheoHoaDon = useCallback(
    async (requestBody: any) => {
      await fetchData(requestBody);
    },
    [fetchData]
  );

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData,
    submitBangKeCongNoTheoHoaDon
  };
};
