import { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import DocumentNumberRange from '../DocumentNumberRange';
import { Label } from '@/components/ui/label';

const OtherTab = () => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving filter template:', data);
    setSaveFilterTemplateDialogOpen(false);
  };

  const handleSaveAnalysisTemplate = (data: SaveTemplateFormData) => {
    setSaveAnalysisTemplateDialogOpen(false);
  };

  return (
    <div className='min-h-[390px] w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số c/từ (từ/đến):</Label>
          <DocumentNumberRange fromDocumentNumberName='so_ct1' toDocumentNumberName='so_ct2' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex items-center gap-2'>
            <FormField
              name='report_filtering'
              type='select'
              options={[{ value: '0', label: 'Người dùng tự lọc' }]}
              className='w-96'
            />

            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu phân tích dữ liệu</Label>
          <div className='flex flex-1 items-center gap-2'>
            <div className='w-96'>
              <FormField
                name='data_analysis_struct'
                type='select'
                options={[{ value: '0', label: 'Không phân tích' }]}
              />
            </div>
            <div className='size-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={873}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mới mẫu phân tích',
                    icon: 7,
                    onClick: () => setSaveAnalysisTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>
      {/* Save Analysis Template Dialog */}
      <SaveTemplateDialog
        open={saveAnalysisTemplateDialogOpen}
        onClose={() => setSaveAnalysisTemplateDialogOpen(false)}
        onSave={handleSaveAnalysisTemplate}
        templateType='analysis'
      />
      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />
    </div>
  );
};

export default OtherTab;
