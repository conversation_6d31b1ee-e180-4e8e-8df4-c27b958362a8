import React from 'react';

import { But<PERSON> } from '@mui/material';
import { useBangKeCongNoTheoHoaDon } from '../hooks/useBangKeCongNoTheoHoaDon';
import { useSearchFieldStates } from '../hooks/useSearchFieldStates';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { transformSearchData } from '../utils/transform-data';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { DetailsTab, OtherTab, BasicInfo } from './tabs';
import { searchSchema, initialValues } from '../schema';
import { AritoIcon } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { toast } from '@/hooks/use-toast';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const searchFieldStates = useSearchFieldStates();
  const { submitBangKeCongNoTheoHoaDon, isLoading } = useBangKeCongNoTheoHoaDon({});

  const handleSubmit = async (data: any) => {
    const searchFieldData = searchFieldStates.getSearchFieldData();
    const transformedData = transformSearchData(data, searchFieldData);

    try {
      await submitBangKeCongNoTheoHoaDon(transformedData);
      onSearch(transformedData);
      onClose();
    } catch (error: any) {
      let errorMessage = 'Có lỗi xảy ra khi tìm kiếm dữ liệu. Vui lòng thử lại.';

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: 'Lỗi',
        description: errorMessage,
        variant: 'destructive',
        duration: 5000
      });
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Băng kê công nợ theo hóa đơn'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab searchFieldStates={searchFieldStates} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
              disabled={isLoading}
            >
              <AritoIcon icon={isLoading ? 885 : 884} marginX='4px' />
              {isLoading ? 'Đang xử lý...' : 'Đồng ý'}
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
