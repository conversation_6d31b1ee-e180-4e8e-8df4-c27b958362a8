import { z } from 'zod';

const getCurrentDate = () => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

const getFirstDayOfMonth = () => {
  const today = new Date();
  return new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
};

export const searchSchema = z.object({
  ngay_bc: z.string().min(1, '<PERSON><PERSON><PERSON> báo cáo không được để trống'),
  ngay_tt: z.string().min(1, '<PERSON><PERSON><PERSON> thanh toán không được để trống'),
  ngay_ct1: z.string().optional().nullable(),
  ngay_ct2: z.string().optional().nullable(),

  ct_theo: z.coerce.number().default(1),
  tt_yn: z.string().default('1'),
  so_ngay_tt: z.coerce.number().default(30),
  so_ngay_cb: z.coerce.number().default(0),

  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),

  ma_unit: z.string().nullable().optional(),
  mau_bc: z.coerce.number().default(20),

  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_bc: getCurrentDate(),
  ngay_tt: getCurrentDate(),

  ct_theo: 1,
  tt_yn: '1',
  so_ngay_tt: 30,
  so_ngay_cb: 0,

  so_ct1: '',
  so_ct2: '',

  ma_unit: null,
  mau_bc: 20,

  data_analysis_struct: '0'
};
