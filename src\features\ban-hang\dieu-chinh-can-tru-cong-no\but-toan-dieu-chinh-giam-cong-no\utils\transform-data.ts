import { format } from 'date-fns';
import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { ChiTietHoaDonBanHangDichVu } from '@/types/schemas';
import { isValidUUID } from '@/lib/uuid-validator';
import { calculateTienVnd } from './calculations';
import { MA_CHUNG_TU } from '@/constants';

const roundToTwoDecimals = (num: number): number => {
  return Math.round((num + Number.EPSILON) * 100) / 100;
};

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @param formMode - Form mode ('add', 'edit', etc.)
 * @param mainDienGiai - Main description from form
 * @param ma_ngv - Type of receipt (1 = by invoice, 2 = by customer)
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (
  detailRows: ChiTietHoaDonBanHangDichVu[] | any,
  formMode: string = 'add',
  mainDienGiai?: string,
  ma_ngv?: string
) => {
  // Ensure detailRows is an array
  if (!Array.isArray(detailRows)) {
    console.warn('detailRows is not an array:', detailRows);
    return [];
  }

  return detailRows.map((row: any, index: number) => {
    // Only include UUID for edit mode, never for add mode (including copy)
    const uuid = formMode === 'edit' && isValidUUID(row.uuid) ? { uuid: row.uuid } : {};

    return {
      line: index + 1,
      ...uuid,

      // Customer information
      ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',

      // Invoice and account information
      id_hd: row.id_hd_data?.ID || row.id_hd || '',
      tk_co: row.id_hd_data?.tk_data?.uuid || row.tk_co_data?.uuid || row.tk_co || '',

      // Exchange rates
      ty_gia_hd: row.ty_gia_hd || 1,
      ty_gia2: row.ty_gia2 || 1,

      // Financial amounts
      tien_nt: calculateTienVnd(row, ma_ngv),
      tien: row.tien || 0,

      // Description - use row's description or fall back to main form description
      dien_giai: row.dien_giai || mainDienGiai || '',

      // Department and organizational units
      ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',
      ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
      ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
      ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
      ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
      ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
      ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
      ma_lsx: row.ma_lsx_data?.uuid || row.ma_lsx || '1',
      ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || '',

      // Additional tracking
      id_tt: parseInt(row.id_tt || '0')
    };
  });
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param totalAmount - Total amount calculated
 * @param entity - Current entity information
 * @param formMode - Form mode ('add', 'edit', etc.)
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: any,
  detailRows: ChiTietHoaDonBanHangDichVu[] | any = [],
  totalAmount: number = 0,
  entity: any = null,
  formMode: string = 'add'
) => {
  // Ensure detailRows is properly handled
  const safeDetailRows = Array.isArray(detailRows) ? detailRows : [];
  const mainDienGiai = data.dien_giai || '';
  const ma_ngv = data.ma_ngv || '1';
  const chi_tiet = transformDetailRows(safeDetailRows, formMode, mainDienGiai, ma_ngv);

  return {
    // Required fields only as per API documentation

    // Basic fields
    ma_ngv: data.ma_ngv || '1',
    dien_giai: data.dien_giai || '',
    tk: state?.taiKhoan?.uuid || '',
    unit_id: entity?.uuid || '',

    // Document number and series from state
    ...transformDocumentNumber(
      state?.quyenChungTu,
      state?.soChungTu,
      MA_CHUNG_TU.BAN_HANG.BUT_TOAN_DIEU_CHINH_GIAM_CONG_NO
    ),
    ngay_ct: data.ngay_ct ? format(new Date(data.ngay_ct), 'yyyy-MM-dd') : '',
    ngay_lct: data.ngay_ct,
    // Currency and exchange rate
    ma_nt: data.ma_nt || '',
    ty_gia: data.ty_gia || 1,

    // Status and flags
    status: data.status || '0',
    transfer_yn: data.transfer_yn !== undefined ? data.transfer_yn : true,
    hd_yn: data.hd_yn !== undefined ? data.hd_yn : false,

    // Document references
    so_ct0: data.so_ct0 || '',
    ngay_ct0: data.ngay_ct0 ? format(new Date(data.ngay_ct0), 'yyyy-MM-dd') : null,
    ma_tt: data.ma_tt || '',

    // Exchange rate settings
    tg_dd: data.tg_dd !== undefined ? data.tg_dd : false,
    cltg_yn: data.cltg_yn !== undefined ? data.cltg_yn : false,

    // Customer and document references
    ma_kh: data.ma_kh || '',
    so_ct_goc: data.so_ct_goc || '',
    dien_giai_ct_goc: data.dien_giai_ct_goc || '',

    // Total amounts
    t_tien_nt: roundToTwoDecimals(totalAmount) || 0,
    t_tien: roundToTwoDecimals(totalAmount) || 0,

    // Detail rows
    chi_tiet
  };
};
