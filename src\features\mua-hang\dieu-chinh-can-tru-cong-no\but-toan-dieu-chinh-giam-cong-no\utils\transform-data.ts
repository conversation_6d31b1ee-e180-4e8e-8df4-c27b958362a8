import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import { FormFieldState } from '../hooks';

export const transformDetailRows = (detailRows: any[], dien_giai: string, formMode: string) => {
  return detailRows.map((row: any, index: number) => {
    const uuid = formMode !== 'add' && isValidUUID(row.uuid) ? { uuid: row.uuid } : {};
    return {
      ...uuid,
      line: index + 1,

      // Customer fields
      ma_kh: row.ma_kh_data?.uuid || '',
      du_cn: row.du_cn || 0,

      id_hd: row.id_hd_data?.ID || '',

      tk_no: row.tk_no_data?.uuid || row.id_hd_data?.tk_data?.uuid || '',

      tien_nt: row.tien_nt || 0,

      dien_giai: row.dien_giai || dien_giai,
      // Reference fields
      ma_bp: row.ma_bp_data?.uuid || '',
      ma_vv: row.ma_vv_data?.uuid || '',
      ma_hd: row.ma_hd_data?.uuid || '',
      ma_dtt: row.ma_dtt_data?.uuid || '',
      ma_ku: row.ma_ku_data?.uuid || '',
      ma_phi: row.ma_phi_data?.uuid || '',
      ma_sp: row.ma_sp_data?.uuid || '',
      ma_lsx: row.ma_lsx_data?.uuid || '',
      ma_cp0: row.ma_cp0_data?.uuid || ''
    };
  });
};

export const transformFormData = (
  data: any,
  state: FormFieldState,
  formMode: string,
  tongTien: number,
  tongThanhToan: number,
  ...rest: any[]
) => {
  const detail = transformDetailRows(rest[0], data.dien_giai, formMode);

  return {
    ...data,
    ty_gia: (data.ty_gia ? parseFloat(data.ty_gia) : 1).toFixed(2) || 1.0,
    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu),
    t_tien: tongTien,
    t_tt: tongThanhToan,
    t_tien_nt: tongTien,
    t_tt_nt: tongThanhToan,
    tk: state.taiKhoan?.uuid || '',
    chi_tiet: detail
  };
};
