import { useState, useEffect, useCallback } from 'react';
import { useButToanDieuChinhGiamCongNo } from '@/hooks';

interface UseButToanDieuChinhGiamCongNoDetailReturn {
  detail: any[];
  isLoadingDetail: boolean;
  error: string | null;
  fetchDetail: () => Promise<void>;
  clearDetail: () => void;
}

/**
 * Custom hook for managing ButToanDieuChinhGiamCongNo detail data
 *
 * This hook handles fetching detail data for a selected ButToanDieuChinhGiamCongNo
 * and provides loading states and error handling.
 */
export const useButToanDieuChinhGiamCongNoDetail = (
  selectedUuid?: string
): UseButToanDieuChinhGiamCongNoDetailReturn => {
  const [detail, setDetail] = useState<any[]>([]);
  const [isLoadingDetail, setIsLoadingDetail] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { getButToanDieuChinhGiamCongNoDetail } = useButToanDieuChinhGiamCongNo();

  const fetchDetail = useCallback(async () => {
    // Clear previous data
    setDetail([]);
    setError(null);

    if (!selectedUuid) {
      setDetail([]);
      return;
    }

    console.log('Fetching detail for UUID:', selectedUuid);
    setIsLoadingDetail(true);

    try {
      const detailData = await getButToanDieuChinhGiamCongNoDetail(selectedUuid);
      console.log('Detail data received:', detailData);

      // The hook now returns the correct array directly
      setDetail(Array.isArray(detailData) ? detailData : []);
    } catch (error) {
      console.error('Error fetching detail:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Không thể tải chi tiết bút toán điều chỉnh giảm công nợ';
      setError(errorMessage);
      setDetail([]);
    } finally {
      setIsLoadingDetail(false);
    }
  }, [selectedUuid, getButToanDieuChinhGiamCongNoDetail]);

  const clearDetail = useCallback(() => {
    setDetail([]);
    setError(null);
  }, []);

  // Auto-fetch when selectedUuid changes
  useEffect(() => {
    if (selectedUuid) {
      fetchDetail();
    } else {
      setDetail([]);
      setError(null);
    }
  }, [selectedUuid]); // Only depend on selectedUuid, not fetchDetail

  return {
    detail,
    isLoadingDetail,
    error,
    fetchDetail,
    clearDetail
  };
};
