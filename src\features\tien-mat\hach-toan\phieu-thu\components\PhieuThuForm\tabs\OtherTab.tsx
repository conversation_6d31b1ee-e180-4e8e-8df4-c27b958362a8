import React from 'react';
import { FileSelectField } from '@/components/custom/arito/form/search-fields';
import { FormField } from '@/components/custom/arito';
import { PhieuThuChiTiet } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface OtherTabProps {
  formMode: FormMode;
  rows: PhieuThuChiTiet[];
}

export const OtherTab: React.FC<OtherTabProps> = ({ formMode, rows }) => {
  const disabled = formMode === 'view';

  return (
    <div className='flex flex-col gap-2 p-4'>
      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'>Mã đối tượng</Label>
        <div className='flex items-center'>
          <FormField name='ma_kh' type='text' disabled={true} value={rows[0]?.ma_kh_data?.customer_code} />
          <span className='text-sm'>{rows[0]?.ma_kh_data?.customer_name}</span>
        </div>
      </div>

      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'>Kèm theo</Label>
        <div className='w-[250px]'>
          <FormField name='so_ct_goc' type='number' defaultValue={0} disabled={disabled} />
        </div>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='text-sm font-medium'>Chọn files</Label>
        <FileSelectField label='' />
      </div>

      <div className='grid grid-cols-[100px,1fr] items-start gap-y-1 sm:grid-cols-[120px,1fr] sm:items-center'>
        <Label className='text-sm font-medium'>Chứng từ gốc</Label>
        <FormField name='dien_giai_ct_goc' type='text' disabled={disabled} />
      </div>
    </div>
  );
};
