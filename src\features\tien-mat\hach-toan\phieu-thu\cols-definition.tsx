import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  customerSearchColumns,
  accountSearchColumns,
  boPhanSearchColumns,
  vuViecSearchColumns,
  hopDongSearchColumns,
  paymentInstallmentSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  dotThanhToanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  hoaDonSearchColumns
} from '@/constants';
import {
  KhachHang,
  AccountModel,
  BoPhan,
  VuViec,
  Contract,
  DotThanhToan,
  KheUoc,
  Phi,
  VatTu,
  ChiPhiKhongHopLeData,
  TaiKhoan
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getReceiptVoucherColumns = (handleOpenViewDialog: () => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    headerAlign: 'center',
    renderCell: params => {
      switch (params.row.status) {
        case '0':
          return 'Chưa ghi sổ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi sổ';
        default:
          return '';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số chứng từ',
    width: 150,
    headerAlign: 'center',
    renderCell: params => (
      <a className='cursor-pointer hover:text-blue-500 hover:underline' onClick={handleOpenViewDialog}>
        {params.row.so_ct}
      </a>
    )
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày chứng từ',
    width: 150,
    headerAlign: 'center'
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 150,
    headerAlign: 'center',
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    headerAlign: 'center',
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250,
    headerAlign: 'center'
  },
  {
    field: 'tk',
    headerName: 'Tài khoản nợ',
    width: 120,
    headerAlign: 'center',
    renderCell: params => params.row.tk_data?.code
  },
  {
    field: 't_tien',
    headerName: 'Tổng tiền',
    width: 150,
    headerAlign: 'center',
    renderCell: (params: GridRenderCellParams) => (
      <div className='text-right'>
        {params.row.t_tien_nt?.toLocaleString('vi-VN', {
          style: 'currency',
          currency: 'VND'
        })}
      </div>
    )
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 100,
    headerAlign: 'center',
    renderCell: params => <div className='text-center'>{params.row.ma_nt_data?.ma_nt}</div>
  },
  {
    field: 'ma_ngv',
    headerName: 'Loại phiếu thu',
    width: 150,
    headerAlign: 'center',
    renderCell: params => {
      switch (params.row.ma_ngv) {
        case '1':
          return '1. Thu theo hóa đơn';
        case '2':
          return '2. Thu theo đối tượng';
        case '3':
          return '3. Thu khác';
        default:
          return '';
      }
    }
  },
  {
    field: 'username2',
    headerName: 'Người sửa',
    width: 150
  },
  {
    field: 'datetime2',
    headerName: 'Ngày sửa',
    width: 150
  }
];

export const getReceiptVoucherDetailColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void,
  ma_ngv?: string,
  dien_giai?: string
): GridColDef[] => {
  switch (ma_ngv) {
    case '1': // Thu theo hóa đơn - Full columns
      return [
        {
          field: 'dien_giai',
          headerName: 'Diễn giải',
          width: 200,
          renderCell: params => (
            <CellField
              name='dien_giai'
              type='text'
              value={params.row.dien_giai || dien_giai || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
            />
          )
        },
        {
          field: 'ma_kh',
          headerName: 'Mã đối tượng',
          width: 120,
          renderCell: params => (
            <SearchField<KhachHang>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={customerSearchColumns}
              columnDisplay='customer_code'
              dialogTitle='Danh mục khách hàng'
              value={params.row.ma_kh_data?.customer_code || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_kh_data', row);
              }}
            />
          )
        },
        {
          field: 'ten_kh',
          headerName: 'Tên đối tượng',
          width: 200,
          renderCell: params => params.row.ma_kh_data?.customer_name
        },
        {
          field: 'du_cn',
          headerName: 'Dư công nợ',
          width: 120,
          renderCell: params => <CellField name='du_cn' type='text' value={params.row.ma_kh_data?.du_cn || ''} />
        },
        {
          field: 'id_hd',
          headerName: 'Hóa đơn',
          width: 100,
          renderCell: params => {
            return (
              <SearchField
                type='text'
                searchEndpoint={`/${QUERY_KEYS.HOA_DON_THEO_KHACH_HANG}/?customer=${params.row.ma_kh_data?.uuid}&context=PT`}
                searchColumns={hoaDonSearchColumns}
                dialogTitle='Hóa đơn'
                value={params.row.id_hd_data?.so_ct}
                onRowSelection={(row: any) => {
                  onCellValueChange(params.row.uuid, 'id_hd_data', row);
                }}
              />
            );
          }
        },
        {
          field: 'so_ct0_hd',
          headerName: 'Số hóa đơn',
          width: 120,
          renderCell: params => <CellField name='so_ct0_hd' type='text' value={params.row.id_hd_data?.so_ct} />
        },
        {
          field: 'ngay_ct_hd',
          headerName: 'Ngày hóa đơn',
          width: 120,
          renderCell: params => <CellField name='ngay_ct_hd' type='date' value={params.row.id_hd_data?.ngay_ct} />
        },
        {
          field: 'tk_co',
          headerName: 'Tài khoản có',
          width: 120,
          renderCell: params => (
            <CellField
              name='tk_co'
              type='text'
              value={params.row.id_hd_data?.tk_data?.tk || params.row.tk_co_data?.code}
            />
          )
        },
        {
          field: 'ma_nt_hd',
          headerName: 'Ngoại tệ',
          width: 100,
          renderCell: params => <CellField name='ma_nt' type='text' value={params.row.id_hd_data?.ngoai_te} />
        },
        {
          field: 'ty_gia_hd',
          headerName: 'Tỷ giá hđ',
          width: 100,
          renderCell: params => (
            <CellField
              name='ty_gia_hd'
              type='number'
              value={params.row.id_hd_data?.ty_gia_hd || params.row.ty_gia_hd}
            />
          )
        },
        {
          field: 'tien_hd_nt',
          headerName: 'Tiền trên hđ',
          width: 100,
          renderCell: params => <CellField name='tien_hd_nt' type='number' value={params.row.id_hd_data?.tien_hd_nt} />
        },
        {
          field: 'da_pb_nt',
          headerName: 'Đã phân bổ',
          width: 120,
          renderCell: params => <CellField name='da_pb_nt' type='text' value={params.row.id_hd_data?.da_pb_nt || ''} />
        },
        {
          field: 'cl_nt',
          headerName: 'Còn lại',
          width: 120,
          renderCell: params => <CellField name='cl_nt' type='number' value={params.row.id_hd_data?.tien_con_phai_tt} />
        },
        {
          field: 'tien_nt',
          headerName: 'Tiền VND',
          width: 150,
          renderCell: params => (
            <CellField
              name='tien_nt'
              type='number'
              value={params.row.tien_nt || params.row.id_hd_data?.tien_con_phai_tt}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
            />
          )
        },
        {
          field: 'ma_bp',
          headerName: 'Bộ phận',
          width: 120,
          renderCell: params => (
            <SearchField<BoPhan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
              searchColumns={boPhanSearchColumns}
              columnDisplay='ma_bp'
              dialogTitle='Danh mục bộ phận'
              value={params.row.ma_bp_data?.ma_bp || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_bp_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_vv',
          headerName: 'Vụ việc',
          width: 120,
          renderCell: params => (
            <SearchField<VuViec>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
              searchColumns={vuViecSearchColumns}
              columnDisplay='ma_vu_viec'
              dialogTitle='Danh mục vụ việc'
              value={params.row.ma_vv_data?.ma_vu_viec || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_vv_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_hd',
          headerName: 'Hợp đồng',
          width: 120,
          renderCell: params => (
            <SearchField<Contract>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
              searchColumns={hopDongSearchColumns}
              columnDisplay='ma_hd'
              dialogTitle='Danh mục hợp đồng'
              value={params.row.ma_hd_data?.ma_hd || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_hd_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_dtt',
          headerName: 'Đợt thanh toán',
          width: 140,
          renderCell: params => (
            <SearchField<DotThanhToan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
              searchColumns={dotThanhToanSearchColumns}
              columnDisplay='ma_dtt'
              dialogTitle='Danh mục đợt thanh toán'
              value={params.row.ma_dtt_data?.ma_dtt || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_dtt_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_ku',
          headerName: 'Khế ước',
          width: 120,
          renderCell: params => (
            <SearchField<KheUoc>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
              searchColumns={kheUocSearchColumns}
              columnDisplay='ma_ku'
              dialogTitle='Danh mục khế ước'
              value={params.row.ma_ku_data?.ma_ku || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_ku_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_phi',
          headerName: 'Phí',
          width: 100,
          renderCell: params => (
            <SearchField<Phi>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.PHI}/`}
              searchColumns={phiSearchColumns}
              columnDisplay='ma_phi'
              dialogTitle='Danh mục phí'
              value={params.row.ma_phi_data?.ma_phi || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_phi_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_sp',
          headerName: 'Sản phẩm',
          width: 120,
          renderCell: params => (
            <SearchField<VatTu>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={vatTuSearchColumns}
              columnDisplay='ma_vt'
              dialogTitle='Danh mục sản phẩm'
              value={params.row.ma_sp_data?.ma_vt || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_sp_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_lsx',
          headerName: 'Lệnh sản xuất',
          width: 120,
          renderCell: params => (
            <CellField
              name='ma_lsx'
              type='text'
              value={params.row.ma_lsx || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_lsx', newValue)}
            />
          )
        },
        {
          field: 'ma_cp0',
          headerName: 'C/p không h/lệ',
          width: 120,
          renderCell: params => (
            <SearchField<ChiPhiKhongHopLeData>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
              searchColumns={chiPhiKhongHopLeSearchColumns}
              columnDisplay='ma_cp_khl'
              dialogTitle='Danh mục chi phí không hợp lệ'
              value={params.row.ma_cp0_data?.ma_cpkhl || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_cp0_data', row);
              }}
            />
          )
        }
      ];

    case '2': // Thu theo đối tượng - Simplified columns
    case '3': // Thu khác - Simplified columns
    default:
      return [
        {
          field: 'dien_giai',
          headerName: 'Diễn giải',
          width: 200,
          renderCell: params => (
            <CellField
              name='dien_giai'
              type='text'
              value={params.row.dien_giai || dien_giai || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
            />
          )
        },
        {
          field: 'ma_kh',
          headerName: 'Mã đối tượng',
          width: 120,
          renderCell: params => (
            <SearchField<KhachHang>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={customerSearchColumns}
              columnDisplay='customer_code'
              dialogTitle='Danh mục khách hàng'
              value={params.row.ma_kh_data?.customer_code || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_kh_data', row);
              }}
            />
          )
        },
        {
          field: 'ten_kh',
          headerName: 'Tên đối tượng',
          width: 200,
          renderCell: params => <div className='px-2 py-1'>{params.row.ma_kh_data?.customer_name || ''}</div>
        },
        {
          field: 'du_cong_no',
          headerName: 'Dư công nợ',
          width: 120,
          renderCell: params => (
            <div className='px-2 py-1 text-right'>
              {params.row.ma_kh_data?.cong_no_phai_thu?.toLocaleString('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }) || '0 ₫'}
            </div>
          )
        },
        {
          field: 'so_hd',
          headerName: 'Số hóa đơn',
          width: 120,
          renderCell: params => (
            <CellField
              name='so_hd'
              type='text'
              value={params.row.id_hd_data?.so_ct || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_hd', newValue)}
            />
          )
        },
        {
          field: 'tk_co',
          headerName: 'Tài khoản có',
          width: 120,
          renderCell: params => (
            <SearchField<AccountModel>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              columnDisplay='code'
              dialogTitle='Danh mục tài khoản'
              value={params.row.tk_co_data?.code || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'tk_co_data', row);
              }}
            />
          )
        },
        {
          field: 'tien',
          headerName: 'Tiền VND',
          width: 150,
          renderCell: params => (
            <CellField
              name='tien'
              type='number'
              value={params.row.tien || params.row.id_hd_data?.tien_con_phai_tt || 0}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien', newValue)}
            />
          )
        },
        {
          field: 'ma_bp',
          headerName: 'Bộ phận',
          width: 120,
          renderCell: params => (
            <SearchField<BoPhan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
              searchColumns={boPhanSearchColumns}
              columnDisplay='ma_bp'
              dialogTitle='Danh mục bộ phận'
              value={params.row.ma_bp_data?.ma_bp || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_bp_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_vv',
          headerName: 'Vụ việc',
          width: 120,
          renderCell: params => (
            <SearchField<VuViec>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
              searchColumns={vuViecSearchColumns}
              columnDisplay='ma_vu_viec'
              dialogTitle='Danh mục vụ việc'
              value={params.row.ma_vv_data?.ma_vu_viec || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_vv_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_hd',
          headerName: 'Hợp đồng',
          width: 120,
          renderCell: params => (
            <SearchField<Contract>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
              searchColumns={hopDongSearchColumns}
              columnDisplay='ma_hd'
              dialogTitle='Danh mục hợp đồng'
              value={params.row.ma_hd_data?.ma_hd || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_hd_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_dtt',
          headerName: 'Đợt thanh toán',
          width: 140,
          renderCell: params => (
            <SearchField<DotThanhToan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
              searchColumns={paymentInstallmentSearchColumns}
              columnDisplay='ma_dtt'
              dialogTitle='Danh mục đợt thanh toán'
              value={params.row.ma_dtt_data?.ma_dtt || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_dtt_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_ku',
          headerName: 'Khế ước',
          width: 120,
          renderCell: params => (
            <SearchField<KheUoc>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
              searchColumns={kheUocSearchColumns}
              columnDisplay='ma_ku'
              dialogTitle='Danh mục khế ước'
              value={params.row.ma_ku_data?.ma_ku || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_ku_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_phi',
          headerName: 'Phí',
          width: 100,
          renderCell: params => (
            <SearchField<Phi>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.PHI}/`}
              searchColumns={phiSearchColumns}
              columnDisplay='ma_phi'
              dialogTitle='Danh mục phí'
              value={params.row.ma_phi_data?.ma_phi || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_phi_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_sp',
          headerName: 'Sản phẩm',
          width: 120,
          renderCell: params => (
            <SearchField<VatTu>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={vatTuSearchColumns}
              columnDisplay='ma_vt'
              dialogTitle='Danh mục sản phẩm'
              value={params.row.ma_sp_data?.ma_vt || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_sp_data', row);
              }}
            />
          )
        },
        {
          field: 'ma_lsx',
          headerName: 'Lệnh sản xuất',
          width: 120,
          renderCell: params => (
            <CellField
              name='ma_lsx'
              type='text'
              value={params.row.ma_lsx || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_lsx', newValue)}
            />
          )
        },
        {
          field: 'ma_cp0',
          headerName: 'C/p không h/lệ',
          width: 120,
          renderCell: params => (
            <SearchField<ChiPhiKhongHopLeData>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
              searchColumns={chiPhiKhongHopLeSearchColumns}
              columnDisplay='ma_cp_khl'
              dialogTitle='Danh mục chi phí không hợp lệ'
              value={params.row.ma_cp0_data?.ma_cpkhl || ''}
              onRowSelection={(row: any) => {
                onCellValueChange(params.row.uuid, 'ma_cp0_data', row);
              }}
            />
          )
        }
      ];
  }
};

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 300,
    renderCell: params => params.row.dien_giai
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 120,
    renderCell: params => params.row.du_cn
  },
  {
    field: 'id_hd',
    headerName: 'Hóa đơn',
    width: 100,
    renderCell: params => params.row.id_hd_data?.so_ct
  },
  {
    field: 'ngay_hd',
    headerName: 'Ngày hóa đơn',
    width: 120,
    renderCell: params => params.row.id_hd_data?.ngay_ct
  },
  {
    field: 'tk_co',
    headerName: 'Tài khoản có',
    width: 120,
    renderCell: params => params.row.tk_co_data?.code
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 100,
    renderCell: params => params.row.id_hd_data?.ngoai_te
  },
  {
    field: 'ty_gia_hd',
    headerName: 'Tỷ giá hđ',
    width: 100,
    renderCell: params => params.row.id_hd_data?.ty_gia_hd
  },
  {
    field: 'da_pb_nt',
    headerName: 'Đã phân bổ',
    width: 120,
    renderCell: params => params.row.id_hd_data?.da_pb_nt
  },
  {
    field: 'cl_nt',
    headerName: 'Còn lại',
    width: 120,
    renderCell: params => params.row.id_hd_data?.tien_con_phai_tt
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150,
    renderCell: params => params.row.tien_nt
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => params.row.ma_bp_data?.ma_bp
  },

  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => params.row.ma_vv_data?.ma_vu_viec
  },

  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => params.row.ma_hd_data?.ma_hd
  },

  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => params.row.ma_dtt_data?.ma_dtt
  },

  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => params.row.ma_ku_data?.ma_ku
  },

  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => params.row.ma_phi_data?.ma_phi
  },

  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_sp_data?.ma_vt
  },

  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 180,
    renderCell: params => params.row.ma_cp0_data?.ma_cpkhl
  }
];
