import { useState } from 'react';
import { GeneralJournalSchema } from '../schemas';

export const useDialogState = () => {
  // Add dialog
  const [showForm, setShowForm] = useState<boolean>(false);

  // Dialog open state
  const [dialogOpen, setDialogOpen] = useState(true);

  // Search parameters
  const [searchParams, setSearchParams] = useState<GeneralJournalSchema | null>(null);

  /**
   * Handle dialog close
   */
  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  /**
   * Handle dialog open
   */
  const handleDialogOpen = () => {
    setDialogOpen(true);
  };

  /**
   * Handle form submission
   * @param values Form values
   */
  const handleFormSubmit = (values: GeneralJournalSchema) => {
    setSearchParams(values);

    // Close the dialog after successful submission
    setDialogOpen(false);

    // Here you would typically make an API call to create the receipt
    // and then handle the response
  };

  return {
    dialogOpen,
    showForm,
    searchParams,
    handleDialogClose,
    handleDialogOpen,
    handleFormSubmit,
    setShowForm
  };
};
