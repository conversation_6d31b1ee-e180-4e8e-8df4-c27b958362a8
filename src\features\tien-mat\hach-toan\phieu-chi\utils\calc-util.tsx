/**
 * Calculate totals from detail rows and tax rows
 */
export const calculateTotals = (detailRows: any[] = [], taxRows: any[] = []) => {
  // Calculate total amount from detail rows
  const tongTien = detailRows.reduce(
    (sum: number, row: { tien_nt: string | number }) => sum + (Number(row.tien_nt as string) || 0),
    0
  );

  // Calculate total tax from both detail rows and tax rows
  const tongThueFromDetail = detailRows.reduce(
    (sum: number, row: { thue_nt: string | number }) => sum + (Number(row.thue_nt) || 0),
    0
  );

  const tongThueFromTax = taxRows.reduce(
    (sum: number, row: { t_thue_nt: string | number }) => sum + (Number(row.t_thue_nt) || 0),
    0
  );

  const tongThue = tongThueFromDetail + tongThueFromTax;

  // Calculate total payment (amount + tax)
  const tongThanhToan = tongTien + tongThue;

  return {
    tongTien,
    tongThue,
    tongThanhToan
  };
};

export const handleUpdateRowFields = (row: any, field: string, newValue: any) => {
  if (field === 'id_hd_data' || field === 'ma_thue_data' || field === 'tien_nt') {
    const tien_nt =
      Number(field === 'id_hd_data' ? newValue?.tien_tren_hd : field === 'tien_nt' ? newValue : row.tien_nt) || 0;
    const thue_suat = Number(field === 'ma_thue_data' ? newValue?.thue_suat : row.thue_suat) || 0;
    const thue_nt = (tien_nt * thue_suat) / 100;
    return {
      tien_nt,
      thue_suat,
      thue_nt
    };
  }
  return {};
};
